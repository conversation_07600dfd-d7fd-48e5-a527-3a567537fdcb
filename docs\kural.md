# 🏆 Restoran POS - 15 Altın Kural

## 1. 🏗️ Feature-Based Klasörleme
```
/features/order/components/OrderList.tsx
/features/order/hooks/useOrderStatus.ts
/features/order/api/orderApi.ts
```

## 2. 💰 Para = Decimal.js
```typescript
const total = new Decimal(price).mul(quantity).toFixed(2)
// ASLA: const total = price * quantity
```

## 3. 🎯 Max 300 Satır Component
Geçerse parçala: `<OrderHeader />`, `<OrderItems />`, `<OrderSummary />`

## 4. 🔐 3 Katmanlı Güvenlik
Frontend (UI) + Backend (API) + Database (Row Level) = Her yerde yetki kontrolü

## 5. 🚀 Optimistic Updates
Önce UI güncelle → Sonra API → Hata varsa rollback

## 6. 🎨 İsimlendirme
- Hooks: `useXxx`
- Handlers: `handleXxx`  
- Booleans: `isXxx`, `hasXxx`
- API: `xxxApi`

## 7. 🔄 Minimal Global State
Sadece auth + tema. Geri kalan = TanStack Query

## 8. ⚡ Lazy Load Everything
```typescript
const ReportsPage = lazy(() => import('@/features/reports'))
```

## 9. 🛡️ Zod Everywhere
API response + Form data + Env variables = Hepsini validate et

## 10. 📱 Touch Friendly
Minimum 44px tıklanabilir alan, mobile-first CSS

## 11. 🔌 Service Layer Pattern
Route (thin) → Service (business logic) → Repository (data)

## 12. 🎯 Error Handling
```typescript
try {
  await doSomething()
} catch (error) {
  toast.error(getUserMessage(error))
  logger.error(error)
}
```

## 13. 🚄 Performance Must
- 100+ item = Virtual scroll
- Search = 300ms debounce  
- Heavy compute = useMemo

## 14. 🧪 Test Kritik İşlemler
Para hesabı + Stok hareketi + Fatura = %100 test coverage

## 15. 📝 Type Safety
`any` yasak → `unknown` kullan → Shared types tek yerde

---

**Quick Check:** Component 300 satırı geçti mi? Para işlemi Decimal mi? Error handling var mı? ✅