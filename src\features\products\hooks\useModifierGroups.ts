import React from 'react'
import { trpc } from '../../../lib/trpc-client'
import { toast } from 'sonner'

/**
 * Modifiyer grup yönetimi için TanStack Query hooks
 * 
 * Bu hooks:
 * - Modifiyer grup listesi getirme
 * - Modifiyer grup detayı getirme
 * - Modifiyer grup oluşturma, güncelleme, silme
 * - Modifiyer oluşturma, güncelleme, silme
 * - Error handling ve toast bildirimleri
 * - Cache invalidation
 */

/**
 * Modifiyer grup listesi getiren hook
 */
export function useModifierGroups(input?: { page?: number; limit?: number }) {
  return trpc.products.modifier.group.list.useQuery(
    input || { page: 1, limit: 50 },
    {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error) => {
        console.error('Modifiyer grup listesi getirme hatası:', error)
        toast.error('Modifiyer grupları yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Tek modifiyer grup detayı getiren hook
 */
export function useModifierGroup(groupId: string) {
  return trpc.products.modifier.group.getById.useQuery(
    { id: groupId },
    {
      enabled: !!groupId,
      staleTime: 5 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Modifiyer grup detayı getirme hatası:', error)
        toast.error('Modifiyer grup detayları yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Modifiyer grup oluşturma hook'u
 */
export function useCreateModifierGroup() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.group.create.useMutation({
    onSuccess: (data) => {
      toast.success('Modifiyer grubu başarıyla oluşturuldu')
      
      // Cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
    },
    onError: (error) => {
      console.error('Modifiyer grup oluşturma hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer grubu oluşturulurken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Modifiyer grup güncelleme hook'u
 */
export function useUpdateModifierGroup() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.group.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Modifiyer grubu başarıyla güncellendi')
      
      // İlgili cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
      utils.products.modifier.group.getById.invalidate({ id: variables.id })
    },
    onError: (error) => {
      console.error('Modifiyer grup güncelleme hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer grubu güncellenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Modifiyer grup silme hook'u
 */
export function useDeleteModifierGroup() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.group.delete.useMutation({
    onSuccess: () => {
      toast.success('Modifiyer grubu başarıyla silindi')
      
      // Cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
    },
    onError: (error) => {
      console.error('Modifiyer grup silme hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer grubu silinirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Modifiyer oluşturma hook'u
 */
export function useCreateModifier() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.item.create.useMutation({
    onSuccess: (data) => {
      toast.success('Modifiyer başarıyla oluşturuldu')
      
      // Cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
    },
    onError: (error) => {
      console.error('Modifiyer oluşturma hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer oluşturulurken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Modifiyer güncelleme hook'u
 */
export function useUpdateModifier() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.item.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Modifiyer başarıyla güncellendi')
      
      // Cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
    },
    onError: (error) => {
      console.error('Modifiyer güncelleme hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer güncellenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Modifiyer silme hook'u
 */
export function useDeleteModifier() {
  const utils = trpc.useUtils()

  return trpc.products.modifier.item.delete.useMutation({
    onSuccess: () => {
      toast.success('Modifiyer başarıyla silindi')
      
      // Cache'leri invalidate et
      utils.products.modifier.group.list.invalidate()
    },
    onError: (error) => {
      console.error('Modifiyer silme hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer silinirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}
