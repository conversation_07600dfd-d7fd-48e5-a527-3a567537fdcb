import React from 'react'
import { PinDotIcon } from '../../../assets/icons/pin-dot-icon'

interface PinInputProps {
  pin: string
  maxLength?: number
  className?: string
}

export function PinInput({ pin, maxLength = 6, className = "" }: PinInputProps) {
  const pinArray = pin.split('')
  const emptySlots = Math.max(0, maxLength - pinArray.length)

  return (
    <div className={`flex gap-3.5 ${className}`}>
      {/* Dolu PIN alanları */}
      {pinArray.map((_, index) => (
        <div
          key={`filled-${index}`}
          className="bg-[#f5f5f5] h-[50px] w-[52px] rounded-xl flex items-center justify-center relative overflow-hidden"
        >
          <PinDotIcon className="w-[11px] h-[11px]" />
        </div>
      ))}
      
      {/* Boş PIN alanları */}
      {Array.from({ length: emptySlots }).map((_, index) => (
        <div
          key={`empty-${index}`}
          className="bg-[#f5f5f5] h-[50px] w-[52px] rounded-xl"
        />
      ))}
    </div>
  )
}
