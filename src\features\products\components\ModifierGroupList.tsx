import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { SearchIcon, EditIcon, PlusIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'
import { useModifierGroups, useDeleteModifierGroup } from '../hooks/useModifierGroups'

/**
 * Modifiyer Grup Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Modifiyer gruplarını ve içindeki modifiyerleri gösterir
 * - Grup bazında genişletme/daraltma özelliği
 * - Arama ve filtreleme
 * - Min/max seçim kuralları gösterimi
 * - Touch-friendly tasarım
 */

interface Modifier {
  id: string
  name: string
  price: string
  active: boolean
  displayOrder: number
}

interface ModifierGroup {
  id: string
  name: string
  description?: string
  minSelection: number
  maxSelection: number
  required: boolean
  active: boolean
  displayOrder: number
  modifiers: Modifier[]
}

interface ModifierGroupListProps {
  searchQuery?: string
  onGroupEdit?: (groupId: string) => void
  onGroupDelete?: (groupId: string) => void
  onModifierEdit?: (modifierId: string) => void
  onModifierDelete?: (modifierId: string) => void
  onNewGroup?: () => void
  onNewModifier?: (groupId: string) => void
}

// Mock data kaldırıldı - artık gerçek API kullanılıyor

export function ModifierGroupList({ 
  searchQuery = '', 
  onGroupEdit, 
  onGroupDelete, 
  onModifierEdit, 
  onModifierDelete, 
  onNewGroup,
  onNewModifier 
}: ModifierGroupListProps) {
  const [localSearch, setLocalSearch] = useState('')
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['1', '2']))
  
  // 300ms debounce for search
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)
  
  // API calls
  const { data: modifierGroupsData, isLoading: isLoadingGroups, error: groupsError } = useModifierGroups({
    page: 1,
    limit: 50
  })

  const deleteModifierGroupMutation = useDeleteModifierGroup()

  // Filtrelenmiş modifiyer grupları
  const filteredGroups = useMemo(() => {
    const groups = modifierGroupsData?.modifierGroups || []

    if (!debouncedSearch) return groups

    return groups.filter(group =>
      group.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
      group.description?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
      group.modifiers.some(modifier =>
        modifier.name.toLowerCase().includes(debouncedSearch.toLowerCase())
      )
    )
  }, [debouncedSearch, modifierGroupsData])
  
  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Modifiyer Grupları</h2>
          <p className="text-sm text-[#636566]">{filteredGroups.length} grup bulundu</p>
        </div>
        
        <Button 
          onClick={onNewGroup}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Grup Ekle
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon className="w-5 h-5 text-gray-400" />
        </div>
        <Input
          type="text"
          placeholder="Modifiyer grubu ara..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10 h-10 rounded-xl border-gray-200"
        />
      </div>

      {/* Modifier Groups */}
      {isLoadingGroups ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">Modifiyer grupları yükleniyor...</p>
        </div>
      ) : groupsError ? (
        <div className="text-center py-12">
          <p className="text-red-500 text-lg mb-4">Modifiyer grupları yüklenirken bir hata oluştu</p>
          <p className="text-gray-400">Lütfen sayfayı yenilemeyi deneyin</p>
        </div>
      ) : filteredGroups.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Modifiyer grubu bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredGroups.map((group) => (
            <ModifierGroupCard
              key={group.id}
              group={group}
              isExpanded={expandedGroups.has(group.id)}
              onToggle={() => toggleGroup(group.id)}
              onEdit={() => onGroupEdit?.(group.id)}
              onDelete={() => onGroupDelete?.(group.id)}
              onModifierEdit={onModifierEdit}
              onModifierDelete={onModifierDelete}
              onNewModifier={() => onNewModifier?.(group.id)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Modifier Group Card Component
interface ModifierGroupCardProps {
  group: ModifierGroup
  isExpanded: boolean
  onToggle: () => void
  onEdit: () => void
  onDelete: () => void
  onModifierEdit?: (modifierId: string) => void
  onModifierDelete?: (modifierId: string) => void
  onNewModifier: () => void
}

function ModifierGroupCard({ 
  group, 
  isExpanded, 
  onToggle, 
  onEdit, 
  onDelete, 
  onModifierEdit, 
  onModifierDelete,
  onNewModifier 
}: ModifierGroupCardProps) {
  const getSelectionText = () => {
    if (group.minSelection === group.maxSelection) {
      return `${group.minSelection} seçim`
    }
    return `${group.minSelection}-${group.maxSelection} seçim`
  }

  return (
    <div className="space-y-2">
      {/* Main Group Card */}
      <Card className="overflow-hidden rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="p-5">
          <div className="flex items-center gap-4">
            {/* Expand/Collapse Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 flex-shrink-0"
            >
              <span className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
                ▶
              </span>
            </Button>
            
            {/* Group Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-[#202325] text-lg">{group.name}</h3>
                {group.required && (
                  <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                    Zorunlu
                  </span>
                )}
                {!group.active && (
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium">
                    Pasif
                  </span>
                )}
              </div>
              
              {group.description && (
                <p className="text-[#636566] text-sm mb-2">{group.description}</p>
              )}
              
              <div className="flex items-center gap-4 text-sm text-[#636566]">
                <span>{group.modifiers.length} modifiyer</span>
                <span>{getSelectionText()}</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 px-4"
              >
                <EditIcon className="w-4 h-4 mr-1" />
                Düzenle
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onDelete}
                className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3"
              >
                Sil
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modifiers */}
      {isExpanded && (
        <div className="ml-12 space-y-2">
          {/* Add New Modifier Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onNewModifier}
            className="w-full h-10 border-dashed border-gray-300 text-gray-500 hover:text-gray-700 hover:border-gray-400"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            Yeni Modifiyer Ekle
          </Button>
          
          {/* Modifier Items */}
          {group.modifiers.map((modifier) => (
            <Card key={modifier.id} className="rounded-xl border border-gray-100 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  {/* Modifier Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold text-[#202325] text-base">{modifier.name}</h4>
                      {!modifier.active && (
                        <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                          Pasif
                        </span>
                      )}
                    </div>
                    <span className="text-sm font-medium text-[#025cca]">
                      {modifier.price === '0.00' ? 'Ücretsiz' : `+$${modifier.price}`}
                    </span>
                  </div>
                  
                  {/* Modifier Actions */}
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onModifierEdit?.(modifier.id)}
                      className="h-8 w-8 p-0 text-[#025cca] hover:bg-[#f0f8ff]"
                    >
                      <EditIcon className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onModifierDelete?.(modifier.id)}
                      className="h-8 w-8 p-0 text-[#ee4e4f] hover:bg-red-50"
                    >
                      ×
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
