import { createRouter, createRoute, createRootRoute, Outlet, createHashHistory } from '@tanstack/react-router'
import { PinLoginPage } from './features/auth/pages/PinLoginPage'
import { DashboardPage } from './features/dashboard/pages/DashboardPage'
import { ProductManagementPage } from './features/products/pages/ProductManagementPage'
import { useAuthStore } from './features/auth/stores/authStore'

console.log('router.tsx loading...')

/**
 * ATROPOS Restaurant POS Router Konfigürasyonu
 * 
 * Bu dosya:
 * - TanStack Router ile route tanımlamaları
 * - Authentication guard'ları
 * - Nested routing yapısı
 * - URL parametreleri ve query string yönetimi
 */

// Root route - tüm route'ların ana container'ı
const rootRoute = createRootRoute({
  component: () => <Outlet />,
  notFoundComponent: () => (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-gray-600 mb-4">Sayfa bulunamadı</p>
        <a
          href="/"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          Ana Sayfaya Dön
        </a>
      </div>
    </div>
  ),
})

// Dashboard route
const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: () => {
    const { isAuthenticated } = useAuthStore()

    if (!isAuthenticated) {
      return <PinLoginPage />
    }

    return <DashboardPage />
  },
})

// Products management route
const productsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/products',
  component: () => {
    const { isAuthenticated } = useAuthStore()

    if (!isAuthenticated) {
      return <PinLoginPage />
    }

    return <ProductManagementPage />
  },
  validateSearch: (search: Record<string, unknown>) => {
    return {
      tab: (search.tab as string) || 'products',
      search: search.search as string,
      category: search.category as string,
      page: Number(search.page) || 1,
    }
  },
})

// Route tree
const routeTree = rootRoute.addChildren([
  dashboardRoute,
  productsRoute,
])

// Electron için hash history oluştur
const isElectron = typeof window !== 'undefined' && window.electronAPI
const hashHistory = isElectron ? createHashHistory() : undefined

// Router instance
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
  // Electron için hash routing kullan
  history: hashHistory,
  defaultNotFoundComponent: () => (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-gray-600 mb-4">Sayfa bulunamadı</p>
        <a
          href="/"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          Ana Sayfaya Dön
        </a>
      </div>
    </div>
  ),
})

// Router type declaration for TypeScript
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
