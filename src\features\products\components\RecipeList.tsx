import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { SearchIcon, EditIcon, PlusIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'

/**
 * Reçete Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Ürün reçetelerini listeler
 * - Malzeme listesi ve miktarları
 * - Verim bilgisi
 * - Maliyet hesaplaması
 * - Touch-friendly tasarım
 */

interface RecipeIngredient {
  id: string
  inventoryItemId: string
  inventoryItemName: string
  quantity: string
  unit: string
  costPerUnit: string
}

interface Recipe {
  id: string
  productId: string
  productName: string
  yield: string
  yieldUnit: string
  totalCost: string
  costPerUnit: string
  active: boolean
  ingredients: RecipeIngredient[]
  lastUpdated: string
}

interface RecipeListProps {
  searchQuery?: string
  onRecipeEdit?: (recipeId: string) => void
  onRecipeDelete?: (recipeId: string) => void
  onNewRecipe?: () => void
}

// Mock data - gerçek API'den gelecek
const mockRecipes: Recipe[] = [
  {
    id: '1',
    productId: 'prod-1',
    productName: 'Margherita Pizza',
    yield: '1',
    yieldUnit: 'adet',
    totalCost: '8.50',
    costPerUnit: '8.50',
    active: true,
    lastUpdated: '2024-01-15',
    ingredients: [
      {
        id: '1-1',
        inventoryItemId: 'inv-1',
        inventoryItemName: 'Pizza Hamuru',
        quantity: '0.3',
        unit: 'kg',
        costPerUnit: '5.00'
      },
      {
        id: '1-2',
        inventoryItemId: 'inv-2',
        inventoryItemName: 'Mozzarella Peyniri',
        quantity: '0.15',
        unit: 'kg',
        costPerUnit: '45.00'
      },
      {
        id: '1-3',
        inventoryItemId: 'inv-3',
        inventoryItemName: 'Domates Sosu',
        quantity: '0.08',
        unit: 'kg',
        costPerUnit: '12.00'
      },
      {
        id: '1-4',
        inventoryItemId: 'inv-4',
        inventoryItemName: 'Fesleğen',
        quantity: '0.01',
        unit: 'kg',
        costPerUnit: '80.00'
      }
    ]
  },
  {
    id: '2',
    productId: 'prod-2',
    productName: 'Caesar Salata',
    yield: '1',
    yieldUnit: 'porsiyon',
    totalCost: '4.25',
    costPerUnit: '4.25',
    active: true,
    lastUpdated: '2024-01-14',
    ingredients: [
      {
        id: '2-1',
        inventoryItemId: 'inv-5',
        inventoryItemName: 'Marul',
        quantity: '0.15',
        unit: 'kg',
        costPerUnit: '8.00'
      },
      {
        id: '2-2',
        inventoryItemId: 'inv-6',
        inventoryItemName: 'Parmesan Peyniri',
        quantity: '0.03',
        unit: 'kg',
        costPerUnit: '120.00'
      },
      {
        id: '2-3',
        inventoryItemId: 'inv-7',
        inventoryItemName: 'Caesar Sos',
        quantity: '0.05',
        unit: 'kg',
        costPerUnit: '25.00'
      },
      {
        id: '2-4',
        inventoryItemId: 'inv-8',
        inventoryItemName: 'Kruton',
        quantity: '0.02',
        unit: 'kg',
        costPerUnit: '15.00'
      }
    ]
  },
  {
    id: '3',
    productId: 'prod-3',
    productName: 'Espresso',
    yield: '1',
    yieldUnit: 'fincan',
    totalCost: '0.85',
    costPerUnit: '0.85',
    active: true,
    lastUpdated: '2024-01-15',
    ingredients: [
      {
        id: '3-1',
        inventoryItemId: 'inv-9',
        inventoryItemName: 'Kahve Çekirdeği',
        quantity: '0.018',
        unit: 'kg',
        costPerUnit: '45.00'
      },
      {
        id: '3-2',
        inventoryItemId: 'inv-10',
        inventoryItemName: 'Su',
        quantity: '0.03',
        unit: 'litre',
        costPerUnit: '0.50'
      }
    ]
  }
]

export function RecipeList({ searchQuery = '', onRecipeEdit, onRecipeDelete, onNewRecipe }: RecipeListProps) {
  const [localSearch, setLocalSearch] = useState('')
  const [expandedRecipes, setExpandedRecipes] = useState<Set<string>>(new Set())
  
  // 300ms debounce for search
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)
  
  // Filtrelenmiş reçeteler
  const filteredRecipes = useMemo(() => {
    if (!debouncedSearch) return mockRecipes
    
    return mockRecipes.filter(recipe => 
      recipe.productName.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
      recipe.ingredients.some(ingredient => 
        ingredient.inventoryItemName.toLowerCase().includes(debouncedSearch.toLowerCase())
      )
    )
  }, [debouncedSearch])
  
  const toggleRecipe = (recipeId: string) => {
    const newExpanded = new Set(expandedRecipes)
    if (newExpanded.has(recipeId)) {
      newExpanded.delete(recipeId)
    } else {
      newExpanded.add(recipeId)
    }
    setExpandedRecipes(newExpanded)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Reçeteler</h2>
          <p className="text-sm text-[#636566]">{filteredRecipes.length} reçete bulundu</p>
        </div>
        
        <Button 
          onClick={onNewRecipe}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Reçete Ekle
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon className="w-5 h-5 text-gray-400" />
        </div>
        <Input
          type="text"
          placeholder="Reçete ara..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10 h-10 rounded-xl border-gray-200"
        />
      </div>

      {/* Recipes */}
      {filteredRecipes.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Reçete bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRecipes.map((recipe) => (
            <RecipeCard
              key={recipe.id}
              recipe={recipe}
              isExpanded={expandedRecipes.has(recipe.id)}
              onToggle={() => toggleRecipe(recipe.id)}
              onEdit={() => onRecipeEdit?.(recipe.id)}
              onDelete={() => onRecipeDelete?.(recipe.id)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Recipe Card Component
interface RecipeCardProps {
  recipe: Recipe
  isExpanded: boolean
  onToggle: () => void
  onEdit: () => void
  onDelete: () => void
}

function RecipeCard({ recipe, isExpanded, onToggle, onEdit, onDelete }: RecipeCardProps) {
  const profitMargin = ((parseFloat(recipe.costPerUnit) / parseFloat(recipe.totalCost)) * 100 - 100).toFixed(1)

  return (
    <div className="space-y-2">
      {/* Main Recipe Card */}
      <Card className="overflow-hidden rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="p-5">
          <div className="flex items-center gap-4">
            {/* Expand/Collapse Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 flex-shrink-0"
            >
              <span className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
                ▶
              </span>
            </Button>
            
            {/* Recipe Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-[#202325] text-lg">{recipe.productName}</h3>
                {!recipe.active && (
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium">
                    Pasif
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-[#636566] mb-2">
                <span>Verim: {recipe.yield} {recipe.yieldUnit}</span>
                <span>{recipe.ingredients.length} malzeme</span>
              </div>
              
              <div className="flex items-center gap-4 text-sm">
                <span className="text-[#636566]">Toplam Maliyet:</span>
                <span className="font-bold text-[#025cca]">${recipe.totalCost}</span>
                <span className="text-[#636566]">Birim Maliyet:</span>
                <span className="font-bold text-[#202325]">${recipe.costPerUnit}</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 px-4"
              >
                <EditIcon className="w-4 h-4 mr-1" />
                Düzenle
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onDelete}
                className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3"
              >
                Sil
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ingredients */}
      {isExpanded && (
        <div className="ml-12 space-y-2">
          <h4 className="font-semibold text-[#202325] text-sm mb-3">Malzemeler:</h4>
          
          {recipe.ingredients.map((ingredient) => (
            <Card key={ingredient.id} className="rounded-xl border border-gray-100 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  {/* Ingredient Info */}
                  <div className="flex-1">
                    <h5 className="font-medium text-[#202325] text-base">{ingredient.inventoryItemName}</h5>
                    <div className="flex items-center gap-4 text-sm text-[#636566] mt-1">
                      <span>Miktar: {ingredient.quantity} {ingredient.unit}</span>
                      <span>Birim Fiyat: ${ingredient.costPerUnit}</span>
                    </div>
                  </div>
                  
                  {/* Cost */}
                  <div className="text-right">
                    <span className="font-semibold text-[#025cca]">
                      ${(parseFloat(ingredient.quantity) * parseFloat(ingredient.costPerUnit)).toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {/* Total Cost Summary */}
          <Card className="rounded-xl border border-blue-100 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <span className="font-semibold text-[#202325]">Toplam Maliyet:</span>
                <span className="font-bold text-[#025cca] text-lg">${recipe.totalCost}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
