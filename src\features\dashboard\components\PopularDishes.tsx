import { usePopularDishes } from "../hooks/usePopularDishes"
import { Skeleton } from "../../../components/ui/skeleton"
import { toast } from "sonner"
import { useEffect } from "react"

/**
 * <PERSON><PERSON>ler yemekler bileşeni
 * 
 * Bu bileşen:
 * - En çok satan ürünleri listeler
 * - <PERSON><PERSON><PERSON><PERSON> adı, sipariş sayısı ve görsel bilgilerini gösterir
 * - API'den gelen verileri görüntüler
 * - Yükleme durumunda skeleton gösterir
 * - Boş durum ve hata durumlarını handle eder
 * - "View All" butonu ile detay sayfasına yönlendirir
 */
export function PopularDishes() {
  const { data: dishes, isLoading, error } = usePopularDishes({ limit: 5 })

  // Hata durumunda toast göster
  useEffect(() => {
    if (error) {
      toast.error('Popüler yemekler yüklenirken hata oluştu')
      console.error('Popular dishes error:', error)
    }
  }, [error])

  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-100 h-[381px]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-[16px] font-semibold text-[#202325]">
          Popüler Yemekler
        </h3>
        <button className="text-[12px] text-[#025CCA] hover:text-blue-700 font-semibold min-h-[44px] px-2">
          Tümünü Gör
        </button>
      </div>

      {/* Content */}
      <div className="space-y-5 overflow-y-auto max-h-[280px]">
        {isLoading ? (
          // Loading skeleton
          <>
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="w-[19px] h-4" />
                <div className="flex items-center gap-4 flex-1">
                  <Skeleton className="w-9 h-9 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
              </div>
            ))}
          </>
        ) : !dishes || dishes.length === 0 ? (
          // Empty state
          <div className="flex items-center justify-center h-[200px]">
            <div className="text-center">
              <p className="text-[12px] text-[#636566] font-normal">
                Bugün henüz hiç yemek satılmadı.
              </p>
            </div>
          </div>
        ) : (
          // Dishes list
          <>
            {dishes.map((dish, index) => (
              <div key={dish.productId} className="flex items-center gap-4">
                <div className="text-[14px] font-medium text-[#202325] w-[19px] text-center">
                  {String(index + 1).padStart(2, "0")}
                </div>
                <div className="flex items-center gap-4 flex-1">
                  <div className="w-9 h-9 bg-gray-200 rounded-full flex-shrink-0 overflow-hidden">
                    {dish.imageUrl ? (
                      <img 
                        src={dish.imageUrl} 
                        alt={dish.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-xs text-gray-400">
                          {dish.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-[14px] font-medium text-[#202325] truncate">
                      {dish.name}
                    </div>
                    <div className="text-[12px] text-[#636566]">
                      Orders :{" "}
                      <span className="font-semibold text-[#202325]">
                        {dish.totalSold}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  )
}
