import { DollarSign, Clock, FileText } from "lucide-react"
import { StatCard } from "./StatCard"
import { useOverviewStats } from "../hooks/useOverviewStats"
import { Skeleton } from "../../../components/ui/skeleton"
import { toast } from "sonner"
import { useEffect } from "react"

/**
 * Dashboard özet kartları bileşeni
 * 
 * Bu bileşen:
 * - Total Earning, In Progress, Waiting List kartlarını gösterir
 * - API'den gelen verileri StatCard bileşenleri ile görüntüler
 * - Yükleme durumunda skeleton gösterir
 * - Hata durumunda toast bildirimi gösterir
 * - Yüzde değişim değerlerini renklendirir (yeşil/kırmızı)
 */
export function OverviewCards() {
  const { data, isLoading, error } = useOverviewStats()

  // Hata durumunda toast göster
  useEffect(() => {
    if (error) {
      toast.error('Özet istatistikler yüklenirken hata oluştu')
      console.error('Overview stats error:', error)
    }
  }, [error])

  if (isLoading) {
    return (
      <div className="grid grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-xl p-5 border border-gray-100">
            <div className="flex items-start justify-between mb-3">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="w-12 h-12 rounded-xl" />
            </div>
            <Skeleton className="h-10 w-16 mb-3" />
            <div className="flex items-center gap-1">
              <Skeleton className="h-3 w-3" />
              <Skeleton className="h-3 w-8" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!data) {
    return (
      <div className="grid grid-cols-3 gap-6">
        <div className="bg-white rounded-xl p-5 border border-gray-100 flex items-center justify-center">
          <p className="text-gray-500 text-sm">Veri yüklenemedi</p>
        </div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-3 gap-6">
      {/* Total Earning Card */}
      <StatCard
        title="Toplam Kazanç"
        value={`₺${data.totalEarning.amount}`}
        change={data.totalEarning.change.value}
        isPositive={data.totalEarning.change.isPositive}
        icon={<DollarSign className="w-6 h-6 text-[#348C60]" />}
        iconBgColor="bg-[#DCF7EA]"
      />

      {/* In Progress Card */}
      <StatCard
        title="Devam Eden"
        value={data.inProgressOrdersCount.count.toString()}
        change={data.inProgressOrdersCount.change.value}
        isPositive={data.inProgressOrdersCount.change.isPositive}
        icon={<Clock className="w-6 h-6 text-[#CA8522]" />}
        iconBgColor="bg-[#FFEDD5]"
      />

      {/* Waiting List Card */}
      <StatCard
        title="Bekleme Listesi"
        value={data.waitingListCount.count.toString()}
        change={data.waitingListCount.change.value}
        isPositive={data.waitingListCount.change.isPositive}
        icon={<FileText className="w-6 h-6 text-[#357DD5]" />}
        iconBgColor="bg-[#DCEEFE]"
      />
    </div>
  )
}
