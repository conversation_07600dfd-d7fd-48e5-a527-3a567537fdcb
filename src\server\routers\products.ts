import { router, protectedProcedure, requireRole } from '../trpc'
import { CategoryService } from '../services/categoryService'
import { ProductService } from '../services/productService'
import { ProductVariantService } from '../services/productVariantService'
import { ModifierService } from '../services/modifierService'
import { TaxService } from '../services/taxService'
import { PriceOverrideService } from '../services/priceOverrideService'
import {
  // Category schemas
  categoryCreateInputSchema,
  categoryUpdateInputSchema,
  categoryListInputSchema,
  categoryOutputSchema,
  categoryListOutputSchema,
  idSchema,

  // Product schemas
  productCreateInputSchema,
  productUpdateInputSchema,
  productListInputSchema,
  productOutputSchema,
  productListOutputSchema,

  // Product Variant schemas
  productVariantCreateInputSchema,
  productVariantUpdateInputSchema,
  productVariantOutputSchema,

  // Modifier schemas
  modifierGroupCreateInputSchema,
  modifierGroupUpdateInputSchema,
  modifierGroupOutputSchema,
  modifierGroupListOutputSchema,
  modifierCreateInputSchema,
  modifierUpdateInputSchema,

  // Product-Modifier relation schemas
  productAddModifierGroupInputSchema,
  productRemoveModifierGroupInputSchema,

  // Tax schemas
  taxCreateInputSchema,
  taxUpdateInputSchema,
  taxOutputSchema,
  taxListOutputSchema,

  // Price Override schemas
  priceOverrideCreateInputSchema,
  priceOverrideUpdateInputSchema,
  priceOverrideListInputSchema,
  priceOverrideOutputSchema,
  priceOverrideListOutputSchema,

  // Success schemas
  successResponseSchema,
  successWithIdResponseSchema,

  // Common schemas
  paginationSchema,
} from '../../lib/validations/products'

/**
 * Products Router - Ürün yönetimi için tRPC prosedürleri
 *
 * Bu router aşağıdaki prosedürleri içerir:
 * - Kategori yönetimi (CRUD + hiyerarşi)
 * - Ürün yönetimi (CRUD + arama + filtreleme)
 * - Ürün varyant yönetimi
 * - Modifiyer grup ve modifiyer yönetimi
 * - Ürün-modifiyer ilişki yönetimi
 * - Vergi yönetimi
 * - Fiyat geçersiz kılma yönetimi
 *
 * Yetkilendirme: SUPER_ADMIN, ADMIN, BRANCH_MANAGER rolleri erişebilir
 */

// Ürün yönetimi için yetki kontrolü
const productManagementProcedure = protectedProcedure.use(
  requireRole(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])
)

export const productsRouter = router({
  // ==================== CATEGORY PROCEDURES ====================

  /**
   * Kategori yönetimi prosedürleri
   */
  category: router({
    list: productManagementProcedure
      .input(categoryListInputSchema)
      .output(categoryListOutputSchema)
      .query(async ({ input, ctx }) => {
        return await CategoryService.getCategories(input, ctx.user!.companyId)
      }),

    getById: productManagementProcedure
      .input(idSchema)
      .output(categoryOutputSchema)
      .query(async ({ input, ctx }) => {
        return await CategoryService.getCategoryById(
          input.id,
          ctx.user!.companyId,
          true // includeChildren
        )
      }),

    create: productManagementProcedure
      .input(categoryCreateInputSchema)
      .output(successWithIdResponseSchema)
      .mutation(async ({ input, ctx }) => {
        const id = await CategoryService.createCategory(input, ctx.user!.companyId)
        return {
          success: true as const,
          id,
          message: 'Kategori başarıyla oluşturuldu',
        }
      }),

    update: productManagementProcedure
      .input(categoryUpdateInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await CategoryService.updateCategory(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Kategori başarıyla güncellendi',
        }
      }),

    delete: productManagementProcedure
      .input(idSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await CategoryService.deleteCategory(input.id, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Kategori başarıyla silindi',
        }
      }),

    getHierarchy: productManagementProcedure
      .output(categoryOutputSchema.array())
      .query(async ({ ctx }) => {
        return await CategoryService.getCategoryHierarchy(ctx.user!.companyId)
      }),
  }),

  // ==================== PRODUCT PROCEDURES ====================

  /**
   * Ürün yönetimi prosedürleri
   */
  product: router({
    list: productManagementProcedure
      .input(productListInputSchema)
      .output(productListOutputSchema)
      .query(async ({ input, ctx }) => {
        return await ProductService.getProducts(input, ctx.user!.companyId)
      }),

    getById: productManagementProcedure
      .input(idSchema)
      .output(productOutputSchema)
      .query(async ({ input, ctx }) => {
        return await ProductService.getProductById(
          input.id,
          ctx.user!.companyId,
          true // includeVariants
        )
      }),

    create: productManagementProcedure
      .input(productCreateInputSchema)
      .output(successWithIdResponseSchema)
      .mutation(async ({ input, ctx }) => {
        const id = await ProductService.createProduct(input, ctx.user!.companyId)
        return {
          success: true as const,
          id,
          message: 'Ürün başarıyla oluşturuldu',
        }
      }),

    update: productManagementProcedure
      .input(productUpdateInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await ProductService.updateProduct(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Ürün başarıyla güncellendi',
        }
      }),

    delete: productManagementProcedure
      .input(idSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await ProductService.deleteProduct(input.id, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Ürün başarıyla silindi',
        }
      }),

    // Ürün-modifiyer ilişki yönetimi
    addModifierGroup: productManagementProcedure
      .input(productAddModifierGroupInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await ProductService.addModifierGroupToProduct(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Modifiyer grubu ürüne başarıyla eklendi',
        }
      }),

    removeModifierGroup: productManagementProcedure
      .input(productRemoveModifierGroupInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await ProductService.removeModifierGroupFromProduct(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Modifiyer grubu üründen başarıyla kaldırıldı',
        }
      }),
  }),

  // ==================== PRODUCT VARIANT PROCEDURES ====================

  /**
   * Ürün varyant yönetimi prosedürleri
   */
  variant: router({
    create: productManagementProcedure
      .input(productVariantCreateInputSchema)
      .output(successWithIdResponseSchema)
      .mutation(async ({ input }) => {
        const id = await ProductVariantService.createProductVariant(input)
        return {
          success: true as const,
          id,
          message: 'Ürün varyantı başarıyla oluşturuldu',
        }
      }),

    update: productManagementProcedure
      .input(productVariantUpdateInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input }) => {
        await ProductVariantService.updateProductVariant(input)
        return {
          success: true as const,
          message: 'Ürün varyantı başarıyla güncellendi',
        }
      }),

    delete: productManagementProcedure
      .input(idSchema)
      .output(successResponseSchema)
      .mutation(async ({ input }) => {
        await ProductVariantService.deleteProductVariant(input.id)
        return {
          success: true as const,
          message: 'Ürün varyantı başarıyla silindi',
        }
      }),

    getById: productManagementProcedure
      .input(idSchema)
      .output(productVariantOutputSchema)
      .query(async ({ input }) => {
        return await ProductVariantService.getProductVariantById(input.id)
      }),

    getByProduct: productManagementProcedure
      .input(idSchema)
      .output(productVariantOutputSchema.array())
      .query(async ({ input }) => {
        return await ProductVariantService.getProductVariants(input.id, true)
      }),
  }),

  // ==================== MODIFIER PROCEDURES ====================

  /**
   * Modifiyer yönetimi prosedürleri
   */
  modifier: router({
    // Modifiyer Grupları
    group: router({
      list: productManagementProcedure
        .input(paginationSchema.partial())
        .output(modifierGroupListOutputSchema)
        .query(async ({ input }) => {
          return await ModifierService.getModifierGroups(
            input.page || 1,
            input.limit || 10,
            true // includeModifiers
          )
        }),

      getById: productManagementProcedure
        .input(idSchema)
        .output(modifierGroupOutputSchema)
        .query(async ({ input }) => {
          return await ModifierService.getModifierGroupById(input.id, true)
        }),

      create: productManagementProcedure
        .input(modifierGroupCreateInputSchema)
        .output(successWithIdResponseSchema)
        .mutation(async ({ input }) => {
          const id = await ModifierService.createModifierGroup(input)
          return {
            success: true as const,
            id,
            message: 'Modifiyer grubu başarıyla oluşturuldu',
          }
        }),

      update: productManagementProcedure
        .input(modifierGroupUpdateInputSchema)
        .output(successResponseSchema)
        .mutation(async ({ input }) => {
          await ModifierService.updateModifierGroup(input)
          return {
            success: true as const,
            message: 'Modifiyer grubu başarıyla güncellendi',
          }
        }),

      delete: productManagementProcedure
        .input(idSchema)
        .output(successResponseSchema)
        .mutation(async ({ input }) => {
          await ModifierService.deleteModifierGroup(input.id)
          return {
            success: true as const,
            message: 'Modifiyer grubu başarıyla silindi',
          }
        }),
    }),

    // Modifiyerler
    item: router({
      create: productManagementProcedure
        .input(modifierCreateInputSchema)
        .output(successWithIdResponseSchema)
        .mutation(async ({ input }) => {
          const id = await ModifierService.createModifier(input)
          return {
            success: true as const,
            id,
            message: 'Modifiyer başarıyla oluşturuldu',
          }
        }),

      update: productManagementProcedure
        .input(modifierUpdateInputSchema)
        .output(successResponseSchema)
        .mutation(async ({ input }) => {
          await ModifierService.updateModifier(input)
          return {
            success: true as const,
            message: 'Modifiyer başarıyla güncellendi',
          }
        }),

      delete: productManagementProcedure
        .input(idSchema)
        .output(successResponseSchema)
        .mutation(async ({ input }) => {
          await ModifierService.deleteModifier(input.id)
          return {
            success: true as const,
            message: 'Modifiyer başarıyla silindi',
          }
        }),
    }),
  }),

  // ==================== TAX PROCEDURES ====================

  /**
   * Vergi yönetimi prosedürleri
   */
  tax: router({
    list: productManagementProcedure
      .output(taxListOutputSchema)
      .query(async ({ ctx }) => {
        return await TaxService.getTaxes(ctx.user!.companyId)
      }),

    getById: productManagementProcedure
      .input(idSchema)
      .output(taxOutputSchema)
      .query(async ({ input, ctx }) => {
        return await TaxService.getTaxById(input.id, ctx.user!.companyId)
      }),

    create: productManagementProcedure
      .input(taxCreateInputSchema)
      .output(successWithIdResponseSchema)
      .mutation(async ({ input, ctx }) => {
        const id = await TaxService.createTax(input, ctx.user!.companyId)
        return {
          success: true as const,
          id,
          message: 'Vergi oranı başarıyla oluşturuldu',
        }
      }),

    update: productManagementProcedure
      .input(taxUpdateInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await TaxService.updateTax(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Vergi oranı başarıyla güncellendi',
        }
      }),

    delete: productManagementProcedure
      .input(idSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await TaxService.deleteTax(input.id, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Vergi oranı başarıyla silindi',
        }
      }),

    getDefault: productManagementProcedure
      .output(taxOutputSchema.nullable())
      .query(async ({ ctx }) => {
        return await TaxService.getDefaultTax(ctx.user!.companyId)
      }),

    getActive: productManagementProcedure
      .output(taxOutputSchema.array())
      .query(async ({ ctx }) => {
        return await TaxService.getActiveTaxes(ctx.user!.companyId)
      }),
  }),

  // ==================== PRICE OVERRIDE PROCEDURES ====================

  /**
   * Fiyat geçersiz kılma yönetimi prosedürleri
   */
  priceOverride: router({
    list: productManagementProcedure
      .input(priceOverrideListInputSchema)
      .output(priceOverrideListOutputSchema)
      .query(async ({ input, ctx }) => {
        return await PriceOverrideService.getPriceOverrides(input, ctx.user!.companyId)
      }),

    getById: productManagementProcedure
      .input(idSchema)
      .output(priceOverrideOutputSchema)
      .query(async ({ input, ctx }) => {
        return await PriceOverrideService.getPriceOverrideById(input.id, ctx.user!.companyId)
      }),

    create: productManagementProcedure
      .input(priceOverrideCreateInputSchema)
      .output(successWithIdResponseSchema)
      .mutation(async ({ input, ctx }) => {
        const id = await PriceOverrideService.createPriceOverride(input, ctx.user!.companyId)
        return {
          success: true as const,
          id,
          message: 'Fiyat geçersiz kılma başarıyla oluşturuldu',
        }
      }),

    update: productManagementProcedure
      .input(priceOverrideUpdateInputSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await PriceOverrideService.updatePriceOverride(input, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Fiyat geçersiz kılma başarıyla güncellendi',
        }
      }),

    delete: productManagementProcedure
      .input(idSchema)
      .output(successResponseSchema)
      .mutation(async ({ input, ctx }) => {
        await PriceOverrideService.deletePriceOverride(input.id, ctx.user!.companyId)
        return {
          success: true as const,
          message: 'Fiyat geçersiz kılma başarıyla silindi',
        }
      }),
  }),
})