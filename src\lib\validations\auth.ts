import { z } from 'zod'
import { UserRole } from '@prisma/client'

// ==================== INPUT SCHEMAS ====================

/**
 * Kullanıcı adı ve şifre ile giriş şeması
 */
export const loginInputSchema = z.object({
  username: z
    .string()
    .min(1, 'Kullanıcı adı gerekli')
    .max(50, 'Kullanıcı adı en fazla 50 karakter olabilir')
    .trim(),
  password: z
    .string()
    .min(1, 'Şifre gerekli')
    .max(100, 'Şifre en fazla 100 karakter olabilir'),
})

/**
 * PIN ile giriş şeması
 */
export const pinLoginInputSchema = z.object({
  userId: z
    .string()
    .cuid('Geçerli bir kullanıcı ID\'si gerekli'),
  pin: z
    .string()
    .length(4, 'PIN 4 haneli olmalıdır')
    .regex(/^\d{4}$/, 'PIN sadece rakam içermelidir'),
})

/**
 * Şifre değiştirme şeması
 */
export const changePasswordInputSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Mevcut şifre gerekli'),
  newPassword: z
    .string()
    .min(6, 'Yeni şifre en az 6 karakter olmalıdır')
    .max(100, 'Yeni şifre en fazla 100 karakter olabilir')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir'
    ),
})

/**
 * PIN değiştirme şeması
 */
export const changePinInputSchema = z.object({
  pin: z
    .string()
    .length(4, 'PIN 4 haneli olmalıdır')
    .regex(/^\d{4}$/, 'PIN sadece rakam içermelidir'),
})

/**
 * Kullanıcı PIN güncelleme şeması (admin için)
 */
export const updateUserPinInputSchema = z.object({
  userId: z
    .string()
    .cuid('Geçerli bir kullanıcı ID\'si gerekli')
    .optional(),
  pin: z
    .string()
    .length(4, 'PIN 4 haneli olmalıdır')
    .regex(/^\d{4}$/, 'PIN sadece rakam içermelidir'),
})

// ==================== OUTPUT SCHEMAS ====================

/**
 * Şirket bilgisi şeması
 */
export const companyInfoSchema = z.object({
  id: z.string(),
  name: z.string(),
})

/**
 * Şube bilgisi şeması
 */
export const branchInfoSchema = z.object({
  id: z.string(),
  name: z.string(),
  code: z.string(),
})

/**
 * Güvenli kullanıcı bilgisi şeması
 */
export const safeUserInfoSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  username: z.string(),
  role: z.nativeEnum(UserRole),
  companyId: z.string(),
  branchId: z.string().nullable(),
  company: companyInfoSchema.optional(),
  branch: branchInfoSchema.optional(),
})

/**
 * Session bilgisi şeması
 */
export const sessionInfoSchema = z.object({
  id: z.string(),
  startedAt: z.date(),
  branchId: z.string(),
})

/**
 * Login response şeması
 */
export const loginResponseSchema = z.object({
  user: safeUserInfoSchema,
  token: z.string().optional(),
  session: sessionInfoSchema.optional(),
})

/**
 * PIN login response şeması
 */
export const pinLoginResponseSchema = z.object({
  user: safeUserInfoSchema,
  token: z.string(),
  session: sessionInfoSchema,
})

/**
 * Kullanıcı listesi (login için) şeması
 */
export const userForLoginSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  username: z.string(),
  role: z.nativeEnum(UserRole),
  companyId: z.string(),
  branchId: z.string().nullable(),
  company: companyInfoSchema.optional(),
  branch: branchInfoSchema.optional(),
})

export const usersForLoginListSchema = z.array(userForLoginSchema)

/**
 * Genel başarı response şeması
 */
export const successResponseSchema = z.object({
  success: z.literal(true),
})

// ==================== FILTER SCHEMAS ====================

/**
 * Kullanıcı listesi filtreleme şeması
 */
export const userListFilterSchema = z.object({
  companyId: z.string().cuid().optional(),
  branchId: z.string().cuid().optional(),
  includeInactive: z.boolean().default(false),
}).optional()

// ==================== VALIDATION HELPERS ====================

/**
 * PIN formatını doğrular
 */
export const validatePinFormat = (pin: string): boolean => {
  return /^\d{4}$/.test(pin)
}

/**
 * Şifre güçlülüğünü doğrular
 */
export const validatePasswordStrength = (password: string): boolean => {
  return password.length >= 6 && /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)
}

/**
 * Kullanıcı adı formatını doğrular
 */
export const validateUsernameFormat = (username: string): boolean => {
  return /^[a-zA-Z0-9_]{3,20}$/.test(username)
}

// ==================== TYPE EXPORTS ====================

export type LoginInput = z.infer<typeof loginInputSchema>
export type PinLoginInput = z.infer<typeof pinLoginInputSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordInputSchema>
export type ChangePinInput = z.infer<typeof changePinInputSchema>
export type UpdateUserPinInput = z.infer<typeof updateUserPinInputSchema>

export type SafeUserInfo = z.infer<typeof safeUserInfoSchema>
export type SessionInfo = z.infer<typeof sessionInfoSchema>
export type LoginResponse = z.infer<typeof loginResponseSchema>
export type PinLoginResponse = z.infer<typeof pinLoginResponseSchema>
export type UserForLogin = z.infer<typeof userForLoginSchema>
export type UsersForLoginList = z.infer<typeof usersForLoginListSchema>
export type SuccessResponse = z.infer<typeof successResponseSchema>
export type UserListFilter = z.infer<typeof userListFilterSchema>
