import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../../../components/ui/dialog'
import { Button } from '../../../../components/ui/button'
import { Input } from '../../../../components/ui/input'
import { Label } from '../../../../components/ui/label'
import { useCategories } from '../../hooks/useCategories'

/**
 * Kategori Form Modal Bileşeni
 * 
 * Bu bileşen:
 * - Yeni kategori ekleme ve mevcut kategori düzenleme
 * - Hiyerarşik kategori yapısı (üst kategori seçimi)
 * - Renk ve ikon seçimi
 * - react-hook-form ve Zod validasyonu
 * - Touch-friendly tasarım
 */

// Form validation schema
const categoryFormSchema = z.object({
  name: z.string().min(1, 'Kategori adı zorunludur').max(100, 'Kategori adı en fazla 100 karakter olabilir'),
  description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
  image: z.string().url('Geçerli bir resim URL\'si gerekli').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Geçerli bir hex renk kodu gerekli').optional(),
  icon: z.string().max(50, 'İkon adı en fazla 50 karakter olabilir').optional(),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
  parentId: z.string().optional(),
  printerGroupId: z.string().optional(),
  preparationTime: z.number().int().min(0).max(999).optional(),
})

type CategoryFormData = z.infer<typeof categoryFormSchema>

interface CategoryFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CategoryFormData) => void
  initialData?: Partial<CategoryFormData>
  mode: 'create' | 'edit'
  isLoading?: boolean
}

// Mock printer groups - bu gerçek API'den gelecek (şimdilik mock)
const mockPrinterGroups = [
  { id: '1', name: 'Mutfak Yazıcısı' },
  { id: '2', name: 'Bar Yazıcısı' },
  { id: '3', name: 'Tatlı Yazıcısı' },
]

const predefinedColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#F7DC6F', '#BB8FCE',
  '#85C1E9', '#82E0AA', '#F8C471', '#F1948A', '#D7DBDD'
]

const predefinedIcons = [
  '🍳', '🍽️', '🥤', '🍰', '🍕', '🍔', '🥗', '☕', '🍜', '🥘'
]

export function CategoryFormModal({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  mode,
  isLoading = false
}: CategoryFormModalProps) {
  // API calls
  const { data: categoriesData } = useCategories({ page: 1, limit: 100, includeChildren: false })
  const parentCategories = categoriesData?.categories || []

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      displayOrder: 0,
      active: true,
      ...initialData
    }
  })

  const selectedColor = watch('color')
  const selectedIcon = watch('icon')

  useEffect(() => {
    if (isOpen && initialData) {
      reset({
        displayOrder: 0,
        active: true,
        ...initialData
      })
    } else if (isOpen) {
      reset()
    }
  }, [isOpen, initialData, reset])

  const handleFormSubmit = (data: CategoryFormData) => {
    onSubmit(data)
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-[#202325]">
            {mode === 'create' ? 'Yeni Kategori Ekle' : 'Kategori Düzenle'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* Temel Bilgiler */}
          <div>
            <Label htmlFor="name">Kategori Adı *</Label>
            <Input
              {...register('name')}
              placeholder="Kategori adını giriniz"
              className="h-10"
            />
            {errors.name && (
              <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="description">Açıklama</Label>
            <textarea
              {...register('description')}
              placeholder="Kategori açıklaması..."
              className="w-full h-20 px-3 py-2 rounded-md border border-gray-200 resize-none"
            />
            {errors.description && (
              <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
            )}
          </div>

          {/* Üst Kategori */}
          <div>
            <Label htmlFor="parentId">Üst Kategori</Label>
            <select
              {...register('parentId')}
              className="w-full h-10 px-3 rounded-md border border-gray-200 bg-white"
            >
              <option value="">Ana kategori olarak ekle</option>
              {parentCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Renk Seçimi */}
          <div>
            <Label>Kategori Rengi</Label>
            <div className="grid grid-cols-5 gap-2 mt-2">
              {predefinedColors.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setValue('color', color)}
                  className={`w-10 h-10 rounded-lg border-2 transition-all ${
                    selectedColor === color ? 'border-gray-400 scale-110' : 'border-gray-200'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
            <Input
              {...register('color')}
              placeholder="#FF6B6B"
              className="h-10 mt-2"
            />
            {errors.color && (
              <p className="text-sm text-red-600 mt-1">{errors.color.message}</p>
            )}
          </div>

          {/* İkon Seçimi */}
          <div>
            <Label>Kategori İkonu</Label>
            <div className="grid grid-cols-5 gap-2 mt-2">
              {predefinedIcons.map((icon) => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setValue('icon', icon)}
                  className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-xl transition-all ${
                    selectedIcon === icon ? 'border-blue-400 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
            <Input
              {...register('icon')}
              placeholder="🍕"
              className="h-10 mt-2"
            />
            {errors.icon && (
              <p className="text-sm text-red-600 mt-1">{errors.icon.message}</p>
            )}
          </div>

          {/* Yazıcı Grubu */}
          <div>
            <Label htmlFor="printerGroupId">Yazıcı Grubu</Label>
            <select
              {...register('printerGroupId')}
              className="w-full h-10 px-3 rounded-md border border-gray-200 bg-white"
            >
              <option value="">Yazıcı grubu seçin</option>
              {mockPrinterGroups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
          </div>

          {/* Ayarlar */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="preparationTime">Hazırlık Süresi (dakika)</Label>
              <Input
                {...register('preparationTime', { valueAsNumber: true })}
                placeholder="0"
                type="number"
                min="0"
                max="999"
                className="h-10"
              />
              {errors.preparationTime && (
                <p className="text-sm text-red-600 mt-1">{errors.preparationTime.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="displayOrder">Görüntüleme Sırası</Label>
              <Input
                {...register('displayOrder', { valueAsNumber: true })}
                placeholder="0"
                type="number"
                min="0"
                className="h-10"
              />
              {errors.displayOrder && (
                <p className="text-sm text-red-600 mt-1">{errors.displayOrder.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="image">Resim URL</Label>
            <Input
              {...register('image')}
              placeholder="https://example.com/image.jpg"
              className="h-10"
            />
            {errors.image && (
              <p className="text-sm text-red-600 mt-1">{errors.image.message}</p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...register('active')}
              className="h-4 w-4"
            />
            <Label>Aktif</Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="h-11 px-6"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="h-11 px-6 bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white"
            >
              {isLoading ? 'Kaydediliyor...' : mode === 'create' ? 'Kategori Ekle' : 'Güncelle'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
