import React from 'react'
import { DeleteIcon } from '../../../assets/icons/delete-icon'

interface PinPadProps {
  onNumberClick: (number: string) => void
  onDeleteClick: () => void
  disabled?: boolean
}

export function PinPad({ onNumberClick, onDeleteClick, disabled = false }: PinPadProps) {
  const numbers = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['.', '0', 'delete']
  ]

  const handleClick = (value: string) => {
    if (disabled) return
    
    if (value === 'delete') {
      onDeleteClick()
    } else if (value === '.') {
      // Nokta tuşu için özel işlem gerekirse buraya eklenebilir
      // Şimdilik PIN girişinde nokta kullanmıyoruz
      return
    } else {
      onNumberClick(value)
    }
  }

  return (
    <div className="grid grid-cols-3 gap-x-[83px] gap-y-2.5 justify-center items-end">
      {numbers.flat().map((value, index) => (
        <button
          key={index}
          onClick={() => handleClick(value)}
          disabled={disabled}
          className={`
            w-16 h-[54px] flex items-center justify-center
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 active:bg-gray-200'}
            transition-colors rounded-lg
            ${value === '.' ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          style={{ minHeight: '44px' }} // Touch-friendly minimum size
        >
          {value === 'delete' ? (
            <DeleteIcon className="w-6 h-6" />
          ) : (
            <span
              className={`
                font-inter text-[28px] text-center text-[#202325] leading-[42px]
                ${value === '.' ? 'font-normal' : 'font-medium'}
              `}
            >
              {value}
            </span>
          )}
        </button>
      ))}
    </div>
  )
}
