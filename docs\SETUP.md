# Restaurant POS - Detaylı Kurulum Rehberi

Bu rehber, Restaurant POS sistemini sıfırdan kurmanız için gereken tüm adımları detaylı olarak açıklar.

## 📋 <PERSON><PERSON> Gere<PERSON>

### 1. Node.js Kurulumu

#### Windows
1. [Node.js resmi sitesine](https://nodejs.org/) gidin
2. LTS versiyonunu (20.x) indirin
3. İndirilen .msi dosyasını çalıştırın
4. Kurulum sihirbazını takip edin
5. Komut istemini açın ve kontrol edin:
   ```cmd
   node --version
   npm --version
   ```

#### macOS
```bash
# Homebrew ile (önerilen)
brew install node@20

# Veya resmi installer ile
# https://nodejs.org/en/download/ adresinden indirin
```

#### Ubuntu/Debian
```bash
# NodeSource repository ekleyin
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -

# Node.js kurun
sudo apt-get install -y nodejs

# Versiyonu kontrol edin
node --version
npm --version
```

### 2. PostgreSQL Kurulumu

#### Windows
1. [PostgreSQL resmi sitesinden](https://www.postgresql.org/download/windows/) indirin
2. Kurulum dosyasını çalıştırın
3. Kurulum sırasında:
   - Port: 5432 (varsayılan)
   - Superuser şifresi belirleyin (unutmayın!)
   - Locale: Turkish, Turkey (opsiyonel)
4. pgAdmin4 kurulumunu da seçin
5. Kurulum tamamlandıktan sonra Windows Servisleri'nde PostgreSQL'in çalıştığını kontrol edin

#### macOS
```bash
# Homebrew ile
brew install postgresql@14
brew services start postgresql@14

# Veya Postgres.app ile
# https://postgresapp.com/ adresinden indirin
```

#### Ubuntu/Debian
```bash
# PostgreSQL kurun
sudo apt update
sudo apt install postgresql postgresql-contrib

# Servisi başlatın
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Durum kontrolü
sudo systemctl status postgresql
```

### 3. Git Kurulumu

#### Windows
1. [Git resmi sitesinden](https://git-scm.com/download/win) indirin
2. Kurulum sihirbazını takip edin
3. Git Bash'i de kurun

#### macOS
```bash
# Xcode Command Line Tools ile
xcode-select --install

# Veya Homebrew ile
brew install git
```

#### Ubuntu/Debian
```bash
sudo apt install git
```

## 🗄️ Veritabanı Kurulumu

### 1. PostgreSQL Kullanıcı ve Veritabanı Oluşturma

#### Windows (pgAdmin4 ile)
1. pgAdmin4'ü açın
2. Sol panelden PostgreSQL sunucusuna bağlanın
3. Databases'e sağ tıklayın → Create → Database
4. Database name: `restaurant_pos`
5. Owner: postgres (veya yeni kullanıcı oluşturun)
6. Save'e tıklayın

#### Komut Satırı ile (Tüm Platformlar)
```bash
# PostgreSQL'e bağlanın
sudo -u postgres psql

# Veritabanı oluşturun
CREATE DATABASE restaurant_pos;

# Yeni kullanıcı oluşturun (opsiyonel)
CREATE USER pos_user WITH PASSWORD 'güçlü_şifre_123';

# Kullanıcıya yetki verin
GRANT ALL PRIVILEGES ON DATABASE restaurant_pos TO pos_user;

# Çıkış yapın
\q
```

### 2. Bağlantı Testi
```bash
# Bağlantıyı test edin
psql -h localhost -p 5432 -U postgres -d restaurant_pos

# Veya yeni kullanıcı ile
psql -h localhost -p 5432 -U pos_user -d restaurant_pos
```

## 📦 Proje Kurulumu

### 1. Projeyi İndirin
```bash
# GitHub'dan klonlayın (URL'yi gerçek repository ile değiştirin)
git clone https://github.com/your-username/restaurant-pos.git
cd restaurant-pos

# Veya ZIP olarak indirip açın
```

### 2. Bağımlılıkları Yükleyin
```bash
# NPM bağımlılıklarını yükleyin
npm install

# Kurulum süresince hata alırsanız:
npm install --legacy-peer-deps

# Veya Yarn kullanıyorsanız:
yarn install
```

### 3. Ortam Değişkenlerini Ayarlayın
```bash
# .env dosyasını oluşturun
cp .env.example .env

# Windows'ta:
copy .env.example .env
```

`.env` dosyasını bir metin editörü ile açın ve düzenleyin:

```env
# Veritabanı bağlantısı - Kendi bilgilerinizi girin
DATABASE_URL="postgresql://kullanıcı_adı:şifre@localhost:5432/restaurant_pos?schema=public"

# Örnek:
# DATABASE_URL="postgresql://postgres:123456@localhost:5432/restaurant_pos?schema=public"
# DATABASE_URL="postgresql://pos_user:güçlü_şifre_123@localhost:5432/restaurant_pos?schema=public"

# Uygulama ayarları
NODE_ENV="development"
PORT=3000

# Güvenlik anahtarları - Üretimde mutlaka değiştirin!
JWT_SECRET="super-secret-jwt-key-change-in-production"
ENCRYPTION_KEY="32-character-encryption-key-here"

# Şirket ayarları
DEFAULT_COMPANY_NAME="Benim Restoranım"
DEFAULT_BRANCH_NAME="Ana Şube"

# Dosya yükleme
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880

# Yazıcı ayarları
ENABLE_PRINTING=false
DEFAULT_PRINTER_IP="*************"

# e-Arşiv ayarları (üretim için)
E_ARCHIVE_TEST_MODE=true
E_ARCHIVE_USERNAME=""
E_ARCHIVE_PASSWORD=""
E_ARCHIVE_ENDPOINT=""

# Log ayarları
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"
```

### 4. Veritabanı Şemasını Oluşturun
```bash
# Prisma client'ı oluşturun
npm run db:generate

# Veritabanı şemasını uygulayın
npm run db:push

# Demo verileri yükleyin
npm run db:seed
```

Bu komutlar başarılı olursa şu çıktıyı görmelisiniz:
```
🌱 Veritabanı seed işlemi başlıyor...
✅ Şirket oluşturuldu: Demo Restaurant
✅ Şube oluşturuldu: Ana Şube
✅ KDV oranları oluşturuldu
✅ Ödeme yöntemleri oluşturuldu
✅ Admin kullanıcı oluşturuldu: admin
✅ Demo kullanıcılar oluşturuldu
✅ Kategoriler oluşturuldu
✅ Ürünler oluşturuldu
✅ Masalar oluşturuldu
✅ Demo müşteri oluşturuldu
🎉 Seed işlemi tamamlandı!

📋 Giriş Bilgileri:
👤 Kullanıcı Adı: admin
🔑 Şifre: admin123
📱 PIN: 1234
```

## 🚀 Uygulamayı Başlatma

### 1. Geliştirme Modu
```bash
# Hem React hem Electron'u başlatın
npm run dev
```

Bu komut:
- React development server'ı başlatır (http://localhost:3000)
- Electron uygulamasını açar
- Hot reload özelliği aktif olur

### 2. Sadece Web Versiyonu
```bash
# Sadece React'i başlatın
npm run dev:react
```

Tarayıcınızda http://localhost:3000 adresine gidin.

### 3. Production Build
```bash
# Production build oluşturun
npm run build

# Electron uygulamasını paketleyin
npm run dist
```

## 🔧 Sorun Giderme

### Yaygın Kurulum Sorunları

#### 1. PostgreSQL Bağlantı Hatası
**Hata:** `Error: connect ECONNREFUSED 127.0.0.1:5432`

**Çözüm:**
```bash
# PostgreSQL çalışıyor mu kontrol edin
# Windows:
services.msc # PostgreSQL servisini arayın

# macOS:
brew services list | grep postgresql

# Linux:
sudo systemctl status postgresql

# Çalışmıyorsa başlatın:
# Windows: Servisler'den başlatın
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql
```

#### 2. Şifre Hatası
**Hata:** `password authentication failed for user "postgres"`

**Çözüm:**
```bash
# PostgreSQL şifresini sıfırlayın
sudo -u postgres psql
ALTER USER postgres PASSWORD 'yeni_şifre';
\q

# .env dosyasındaki DATABASE_URL'i güncelleyin
```

#### 3. Port Çakışması
**Hata:** `Error: listen EADDRINUSE :::3000`

**Çözüm:**
```bash
# 3000 portunu kullanan süreci bulun
# Windows:
netstat -ano | findstr :3000

# macOS/Linux:
lsof -i :3000

# Süreci sonlandırın veya farklı port kullanın
PORT=3001 npm run dev
```

#### 4. Node.js Versiyon Hatası
**Hata:** `The engine "node" is incompatible with this module`

**Çözüm:**
```bash
# Node.js versiyonunu kontrol edin
node --version

# 20.x versiyonu kurulu değilse güncelleyin
# Windows: Resmi siteden indirin
# macOS: brew install node@20
# Linux: NodeSource repository kullanın
```

#### 5. NPM Bağımlılık Hatası
**Hata:** `npm ERR! peer dep missing`

**Çözüm:**
```bash
# Cache'i temizleyin
npm cache clean --force

# node_modules'ı silin ve yeniden kurun
rm -rf node_modules package-lock.json
npm install

# Veya legacy peer deps ile kurun
npm install --legacy-peer-deps
```

#### 6. Prisma Client Hatası
**Hata:** `PrismaClientInitializationError`

**Çözüm:**
```bash
# Prisma client'ı yeniden oluşturun
npm run db:generate

# Şemayı yeniden uygulayın
npm run db:push
```

### Veritabanı Sorunları

#### 1. Tablo Bulunamadı Hatası
```bash
# Şemayı kontrol edin
npm run db:studio

# Şemayı yeniden uygulayın
npm run db:push

# Seed'i yeniden çalıştırın
npm run db:seed
```

#### 2. Migration Hatası
```bash
# Migration'ları sıfırlayın
npx prisma migrate reset

# Yeni migration oluşturun
npm run db:migrate
```

## 📱 İlk Kullanım

### 1. Giriş Yapma
Uygulama açıldığında giriş ekranını göreceksiniz:

**Admin Kullanıcı:**
- Kullanıcı Adı: `admin`
- Şifre: `admin123`

**PIN ile Hızlı Giriş:**
- PIN: `1234`

### 2. Demo Verileri
Sistem aşağıdaki demo verilerle gelir:
- 1 şirket (Demo Restaurant)
- 1 şube (Ana Şube)
- 4 kategori (Sıcak İçecekler, Soğuk İçecekler, Ana Yemekler, Tatlılar)
- 5 ürün
- 3 masa
- 3 kullanıcı (admin, kasiyer1, garson1)
- KDV oranları (%8, %18)
- Ödeme yöntemleri (Nakit, Kredi Kartı, Yemek Kartı)

### 3. İlk Sipariş
1. Ana ekranda "Yeni Sipariş" butonuna tıklayın
2. Sipariş tipini seçin
3. Ürünleri sepete ekleyin
4. Siparişi kaydedin

## 🔄 Güncelleme

### Proje Güncellemeleri
```bash
# Git ile güncellemeleri çekin
git pull origin main

# Bağımlılıkları güncelleyin
npm install

# Veritabanı değişiklikleri varsa
npm run db:generate
npm run db:push
```

### Bağımlılık Güncellemeleri
```bash
# Güncel olmayan paketleri kontrol edin
npm outdated

# Güvenlik açıklarını kontrol edin
npm audit

# Güvenlik güncellemelerini uygulayın
npm audit fix
```

## 📞 Destek

Kurulum sırasında sorun yaşarsanız:

1. **Hata loglarını kontrol edin:** Konsol çıktısını ve hata mesajlarını kaydedin
2. **GitHub Issues:** Sorunları GitHub repository'sindeki Issues bölümünde arayın
3. **Yeni Issue:** Bulamazsanız yeni bir issue oluşturun ve şunları ekleyin:
   - İşletim sistemi ve versiyonu
   - Node.js versiyonu
   - PostgreSQL versiyonu
   - Tam hata mesajı
   - Hangi adımda hata aldığınız

## ✅ Kurulum Kontrol Listesi

- [ ] Node.js 20.x kuruldu
- [ ] PostgreSQL 14.x kuruldu ve çalışıyor
- [ ] Git kuruldu
- [ ] Proje klonlandı/indirildi
- [ ] `npm install` başarılı
- [ ] `.env` dosyası oluşturuldu ve düzenlendi
- [ ] Veritabanı oluşturuldu
- [ ] `npm run db:generate` başarılı
- [ ] `npm run db:push` başarılı
- [ ] `npm run db:seed` başarılı
- [ ] `npm run dev` başarılı
- [ ] Uygulama açıldı
- [ ] Admin kullanıcı ile giriş yapıldı

Tüm adımlar tamamlandığında Restaurant POS sistemi kullanıma hazır olacaktır!

---

**İyi çalışmalar!** 🚀

