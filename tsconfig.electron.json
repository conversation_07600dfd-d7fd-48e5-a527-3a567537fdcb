{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "noEmit": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2020"], "jsx": "react-jsx", "resolveJsonModule": true, "isolatedModules": true}, "include": ["electron/**/*"], "exclude": ["src/**/*", "node_modules", "dist"]}