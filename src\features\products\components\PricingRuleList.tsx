import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { SearchIcon, EditIcon, PlusIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'

/**
 * Fiyatlandırma Kuralları Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Fiyat geçersiz kılma kurallarını listeler
 * - <PERSON>ral türleri (indirim, artış, sabit fiyat)
 * - Geçerlilik tarihleri
 * - Ürün/kategori bazlı kurallar
 * - Touch-friendly tasarım
 */

interface PricingRule {
  id: string
  name: string
  description?: string
  type: 'DISCOUNT' | 'MARKUP' | 'FIXED_PRICE'
  value: string
  valueType: 'PERCENTAGE' | 'AMOUNT'
  targetType: 'PRODUCT' | 'CATEGORY' | 'ALL'
  targetId?: string
  targetName?: string
  startDate: string
  endDate?: string
  active: boolean
  priority: number
  conditions?: {
    minQuantity?: number
    minAmount?: string
    dayOfWeek?: string[]
    timeRange?: {
      start: string
      end: string
    }
  }
  createdAt: string
}

interface PricingRuleListProps {
  searchQuery?: string
  onRuleEdit?: (ruleId: string) => void
  onRuleDelete?: (ruleId: string) => void
  onNewRule?: () => void
}

// Mock data - gerçek API'den gelecek
const mockPricingRules: PricingRule[] = [
  {
    id: '1',
    name: 'Kahvaltı İndirimi',
    description: 'Sabah saatlerinde kahvaltı menüsü için %15 indirim',
    type: 'DISCOUNT',
    value: '15',
    valueType: 'PERCENTAGE',
    targetType: 'CATEGORY',
    targetId: 'cat-1',
    targetName: 'Kahvaltı',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    active: true,
    priority: 1,
    conditions: {
      timeRange: {
        start: '07:00',
        end: '11:00'
      }
    },
    createdAt: '2024-01-01'
  },
  {
    id: '2',
    name: 'Hafta Sonu Artışı',
    description: 'Cumartesi ve Pazar günleri %10 artış',
    type: 'MARKUP',
    value: '10',
    valueType: 'PERCENTAGE',
    targetType: 'ALL',
    startDate: '2024-01-01',
    active: true,
    priority: 2,
    conditions: {
      dayOfWeek: ['SATURDAY', 'SUNDAY']
    },
    createdAt: '2024-01-01'
  },
  {
    id: '3',
    name: 'Öğrenci İndirimi',
    description: 'Öğrenci kartı ile %20 indirim',
    type: 'DISCOUNT',
    value: '20',
    valueType: 'PERCENTAGE',
    targetType: 'ALL',
    startDate: '2024-01-01',
    endDate: '2024-06-30',
    active: true,
    priority: 3,
    conditions: {
      minAmount: '25.00'
    },
    createdAt: '2024-01-01'
  },
  {
    id: '4',
    name: 'Happy Hour İçecek',
    description: 'Akşam saatlerinde içeceklerde sabit fiyat',
    type: 'FIXED_PRICE',
    value: '5.00',
    valueType: 'AMOUNT',
    targetType: 'CATEGORY',
    targetId: 'cat-3',
    targetName: 'İçecekler',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    active: true,
    priority: 4,
    conditions: {
      timeRange: {
        start: '17:00',
        end: '19:00'
      }
    },
    createdAt: '2024-01-01'
  },
  {
    id: '5',
    name: 'Grup İndirimi',
    description: '5 ve üzeri ürün alımında %25 indirim',
    type: 'DISCOUNT',
    value: '25',
    valueType: 'PERCENTAGE',
    targetType: 'ALL',
    startDate: '2024-01-01',
    active: false,
    priority: 5,
    conditions: {
      minQuantity: 5
    },
    createdAt: '2024-01-01'
  }
]

export function PricingRuleList({ searchQuery = '', onRuleEdit, onRuleDelete, onNewRule }: PricingRuleListProps) {
  const [localSearch, setLocalSearch] = useState('')
  const [typeFilter, setTypeFilter] = useState<'all' | 'DISCOUNT' | 'MARKUP' | 'FIXED_PRICE'>('all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  
  // 300ms debounce for search
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)
  
  // Filtrelenmiş fiyatlandırma kuralları
  const filteredRules = useMemo(() => {
    let rules = mockPricingRules
    
    // Arama filtresi
    if (debouncedSearch) {
      rules = rules.filter(rule => 
        rule.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        rule.description?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        rule.targetName?.toLowerCase().includes(debouncedSearch.toLowerCase())
      )
    }
    
    // Tür filtresi
    if (typeFilter !== 'all') {
      rules = rules.filter(rule => rule.type === typeFilter)
    }
    
    // Durum filtresi
    if (statusFilter === 'active') {
      rules = rules.filter(rule => rule.active)
    } else if (statusFilter === 'inactive') {
      rules = rules.filter(rule => !rule.active)
    }
    
    return rules.sort((a, b) => a.priority - b.priority)
  }, [debouncedSearch, typeFilter, statusFilter])
  
  const getRuleTypeInfo = (type: string) => {
    switch (type) {
      case 'DISCOUNT':
        return { label: 'İndirim', color: 'bg-green-100 text-green-700', icon: '↓' }
      case 'MARKUP':
        return { label: 'Artış', color: 'bg-red-100 text-red-700', icon: '↑' }
      case 'FIXED_PRICE':
        return { label: 'Sabit Fiyat', color: 'bg-blue-100 text-blue-700', icon: '=' }
      default:
        return { label: 'Bilinmeyen', color: 'bg-gray-100 text-gray-700', icon: '?' }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Fiyatlandırma Kuralları</h2>
          <p className="text-sm text-[#636566]">{filteredRules.length} kural bulundu</p>
        </div>
        
        <Button 
          onClick={onNewRule}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Kural Ekle
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4 flex-wrap">
        {/* Search Bar */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon className="w-5 h-5 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Fiyatlandırma kuralı ara..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="pl-10 h-10 rounded-xl border-gray-200"
          />
        </div>
        
        {/* Type Filter */}
        <div className="flex gap-2">
          <Button
            variant={typeFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTypeFilter('all')}
            className="h-10 px-4"
          >
            Tümü
          </Button>
          <Button
            variant={typeFilter === 'DISCOUNT' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTypeFilter('DISCOUNT')}
            className="h-10 px-4 text-green-600 border-green-200 hover:bg-green-50"
          >
            İndirim
          </Button>
          <Button
            variant={typeFilter === 'MARKUP' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTypeFilter('MARKUP')}
            className="h-10 px-4 text-red-600 border-red-200 hover:bg-red-50"
          >
            Artış
          </Button>
          <Button
            variant={typeFilter === 'FIXED_PRICE' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTypeFilter('FIXED_PRICE')}
            className="h-10 px-4 text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            Sabit Fiyat
          </Button>
        </div>
        
        {/* Status Filter */}
        <div className="flex gap-2">
          <Button
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('all')}
            className="h-10 px-4"
          >
            Tüm Durumlar
          </Button>
          <Button
            variant={statusFilter === 'active' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('active')}
            className="h-10 px-4"
          >
            Aktif
          </Button>
          <Button
            variant={statusFilter === 'inactive' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('inactive')}
            className="h-10 px-4"
          >
            Pasif
          </Button>
        </div>
      </div>

      {/* Pricing Rules */}
      {filteredRules.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Fiyatlandırma kuralı bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRules.map((rule) => (
            <PricingRuleCard
              key={rule.id}
              rule={rule}
              onEdit={() => onRuleEdit?.(rule.id)}
              onDelete={() => onRuleDelete?.(rule.id)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Pricing Rule Card Component
interface PricingRuleCardProps {
  rule: PricingRule
  onEdit: () => void
  onDelete: () => void
}

function PricingRuleCard({ rule, onEdit, onDelete }: PricingRuleCardProps) {
  const typeInfo = getRuleTypeInfo(rule.type)
  
  function getRuleTypeInfo(type: string) {
    switch (type) {
      case 'DISCOUNT':
        return { label: 'İndirim', color: 'bg-green-100 text-green-700', icon: '↓' }
      case 'MARKUP':
        return { label: 'Artış', color: 'bg-red-100 text-red-700', icon: '↑' }
      case 'FIXED_PRICE':
        return { label: 'Sabit Fiyat', color: 'bg-blue-100 text-blue-700', icon: '=' }
      default:
        return { label: 'Bilinmeyen', color: 'bg-gray-100 text-gray-700', icon: '?' }
    }
  }
  
  const formatValue = () => {
    if (rule.valueType === 'PERCENTAGE') {
      return `${rule.value}%`
    } else {
      return `$${rule.value}`
    }
  }
  
  const formatDateRange = () => {
    const start = new Date(rule.startDate).toLocaleDateString('tr-TR')
    if (rule.endDate) {
      const end = new Date(rule.endDate).toLocaleDateString('tr-TR')
      return `${start} - ${end}`
    }
    return `${start} - Süresiz`
  }
  
  const getConditionsText = () => {
    const conditions = []
    
    if (rule.conditions?.timeRange) {
      conditions.push(`${rule.conditions.timeRange.start} - ${rule.conditions.timeRange.end}`)
    }
    
    if (rule.conditions?.dayOfWeek) {
      const days = rule.conditions.dayOfWeek.map(day => {
        const dayMap: Record<string, string> = {
          'MONDAY': 'Pzt',
          'TUESDAY': 'Sal',
          'WEDNESDAY': 'Çar',
          'THURSDAY': 'Per',
          'FRIDAY': 'Cum',
          'SATURDAY': 'Cmt',
          'SUNDAY': 'Paz'
        }
        return dayMap[day] || day
      })
      conditions.push(days.join(', '))
    }
    
    if (rule.conditions?.minQuantity) {
      conditions.push(`Min ${rule.conditions.minQuantity} adet`)
    }
    
    if (rule.conditions?.minAmount) {
      conditions.push(`Min $${rule.conditions.minAmount}`)
    }
    
    return conditions.length > 0 ? conditions.join(' • ') : null
  }

  return (
    <Card className="rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-5">
        <div className="flex items-start gap-4">
          {/* Rule Type Badge */}
          <div className={`px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ${typeInfo.color} flex-shrink-0`}>
            <span>{typeInfo.icon}</span>
            <span>{typeInfo.label}</span>
          </div>
          
          {/* Rule Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-bold text-[#202325] text-lg">{rule.name}</h3>
              <span className="text-lg font-bold text-[#025cca]">{formatValue()}</span>
              {!rule.active && (
                <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full font-medium">
                  Pasif
                </span>
              )}
            </div>
            
            {rule.description && (
              <p className="text-[#636566] text-sm mb-2">{rule.description}</p>
            )}
            
            <div className="space-y-1 text-sm text-[#636566]">
              <div className="flex items-center gap-4">
                <span>Hedef: {rule.targetType === 'ALL' ? 'Tüm Ürünler' : rule.targetName || 'Belirtilmemiş'}</span>
                <span>Öncelik: {rule.priority}</span>
              </div>
              
              <div>Geçerlilik: {formatDateRange()}</div>
              
              {getConditionsText() && (
                <div>Koşullar: {getConditionsText()}</div>
              )}
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 px-4"
            >
              <EditIcon className="w-4 h-4 mr-1" />
              Düzenle
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onDelete}
              className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3"
            >
              Sil
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
