import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type {
  ProductCreateInput,
  ProductUpdateInput,
  ProductListInput,
  ProductOutput,
  ProductListOutput,
  ProductAddModifierGroupInput,
  ProductRemoveModifierGroupInput
} from '../../lib/validations/products'

/**
 * ProductService - Ürün yönetimi için servis katmanı
 *
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Ürün CRUD işlemleri
 * - Ürün-modifiyer ilişki yönetimi
 * - Decimal.js ile güvenli para hesaplamaları
 */
export class ProductService {
  /**
   * Ürün listesini getirir
   * @param input - Filtreleme ve sayfalama parametreleri
   * @param companyId - Şirket ID'si
   * @returns Ürün listesi
   */
  static async getProducts(
    input: ProductListInput,
    companyId: string
  ): Promise<ProductListOutput> {
    const { 
      categoryId,
      search,
      active,
      available,
      sellable,
      hasVariants,
      hasModifiers,
      trackStock,
      includeVariants = false,
      page = 1, 
      limit = 10 
    } = input

    const skip = (page - 1) * limit

    // Where koşulları
    const where = {
      companyId,
      ...(categoryId && { categoryId }),
      ...(active !== undefined && { active }),
      ...(available !== undefined && { available }),
      ...(sellable !== undefined && { sellable }),
      ...(hasVariants !== undefined && { hasVariants }),
      ...(hasModifiers !== undefined && { hasModifiers }),
      ...(trackStock !== undefined && { trackStock }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { code: { contains: search, mode: 'insensitive' as const } },
          { barcode: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
    }

    // Include koşulları
    const include = {
      category: {
        select: {
          id: true,
          name: true,
          color: true,
          icon: true,
        },
      },
      tax: {
        select: {
          id: true,
          name: true,
          rate: true,
          code: true,
        },
      },
      ...(includeVariants && {
        variants: {
          where: { active: true },
          orderBy: { displayOrder: 'asc' as const },
          select: {
            id: true,
            name: true,
            code: true,
            price: true,
            displayOrder: true,
            active: true,
          },
        },
      }),

    }

    // Ürünleri getir
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include,
        orderBy: { displayOrder: 'asc' },
        skip: limit ? skip : undefined,
        take: limit || undefined,
      }),
      prisma.product.count({ where }),
    ])

    const totalPages = limit ? Math.ceil(total / limit) : 1

    // Decimal değerleri string'e çevir
    const formattedProducts = products.map(product => ({
      ...product,
      basePrice: product.basePrice.toString(),
      criticalStock: product.criticalStock?.toString() || null,
      tax: product.tax ? {
        ...product.tax,
        rate: product.tax.rate.toString(),
      } : undefined,
      variants: product.variants?.map(variant => ({
        ...variant,
        price: variant.price.toString(),
      })),
    }))

    return {
      products: formattedProducts as ProductOutput[],
      total,
      page,
      limit: limit || total,
      totalPages,
    }
  }

  /**
   * ID'ye göre ürün getirir
   * @param id - Ürün ID'si
   * @param companyId - Şirket ID'si
   * @param includeVariants - Varyantları dahil et
   * @param includeModifiers - Modifiyerleri dahil et
   * @returns Ürün bilgisi
   */
  static async getProductById(
    id: string,
    companyId: string,
    includeVariants = true
  ): Promise<ProductOutput> {
    const product = await prisma.product.findFirst({
      where: { id, companyId },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true,
            code: true,
          },
        },
        ...(includeVariants && {
          variants: {
            where: { active: true },
            orderBy: { displayOrder: 'asc' },
            select: {
              id: true,
              name: true,
              code: true,
              price: true,
              displayOrder: true,
              active: true,
            },
          },
        }),

      },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Decimal değerleri string'e çevir
    const formattedProduct = {
      ...product,
      basePrice: product.basePrice.toString(),
      criticalStock: product.criticalStock?.toString() || null,
      tax: product.tax ? {
        ...product.tax,
        rate: product.tax.rate.toString(),
      } : undefined,
      variants: product.variants?.map(variant => ({
        ...variant,
        price: variant.price.toString(),
      })),
    }

    return formattedProduct as ProductOutput
  }

  /**
   * Yeni ürün oluşturur
   * @param input - Ürün bilgileri
   * @param companyId - Şirket ID'si
   * @returns Oluşturulan ürün ID'si
   */
  static async createProduct(
    input: ProductCreateInput,
    companyId: string
  ): Promise<string> {
    const {
      categoryId,
      code,
      barcode,
      name,
      description,
      image,
      basePrice,
      taxId,
      trackStock,
      unit,
      criticalStock,
      available,
      sellable,
      preparationTime,
      hasVariants,
      hasModifiers,
      displayOrder,
      active,
    } = input

    // Aynı kodda ürün kontrolü
    const existingProduct = await prisma.product.findFirst({
      where: {
        companyId,
        code,
      },
    })

    if (existingProduct) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu kodda bir ürün zaten mevcut',
      })
    }

    // Barkod kontrolü
    if (barcode) {
      const existingBarcode = await prisma.product.findFirst({
        where: {
          companyId,
          barcode,
        },
      })

      if (existingBarcode) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu barkodda bir ürün zaten mevcut',
        })
      }
    }

    // Kategori kontrolü
    const category = await prisma.category.findFirst({
      where: { id: categoryId, companyId },
    })

    if (!category) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kategori bulunamadı',
      })
    }

    // Vergi kontrolü
    const tax = await prisma.tax.findFirst({
      where: { id: taxId, companyId },
    })

    if (!tax) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Vergi oranı bulunamadı',
      })
    }

    // Decimal dönüşümleri
    const basePriceDecimal = new Decimal(basePrice)
    const criticalStockDecimal = criticalStock ? new Decimal(criticalStock) : null

    // Ürün oluştur
    const product = await prisma.product.create({
      data: {
        companyId,
        categoryId,
        code,
        barcode,
        name,
        description,
        image,
        basePrice: basePriceDecimal,
        taxId,
        trackStock,
        unit,
        criticalStock: criticalStockDecimal,
        available,
        sellable,
        preparationTime,
        hasVariants,
        hasModifiers,
        displayOrder,
        active,
      },
    })

    return product.id
  }

  /**
   * Ürün günceller
   * @param input - Güncellenecek ürün bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async updateProduct(
    input: ProductUpdateInput,
    companyId: string
  ): Promise<boolean> {
    const { id, ...updateData } = input

    // Ürün varlık kontrolü
    const existingProduct = await prisma.product.findFirst({
      where: { id, companyId },
    })

    if (!existingProduct) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Kod değişikliği kontrolü
    if (updateData.code && updateData.code !== existingProduct.code) {
      const duplicateProduct = await prisma.product.findFirst({
        where: {
          companyId,
          code: updateData.code,
          id: { not: id },
        },
      })

      if (duplicateProduct) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu kodda bir ürün zaten mevcut',
        })
      }
    }

    // Barkod değişikliği kontrolü
    if (updateData.barcode && updateData.barcode !== existingProduct.barcode) {
      const duplicateBarcode = await prisma.product.findFirst({
        where: {
          companyId,
          barcode: updateData.barcode,
          id: { not: id },
        },
      })

      if (duplicateBarcode) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu barkodda bir ürün zaten mevcut',
        })
      }
    }

    // Kategori kontrolü
    if (updateData.categoryId) {
      const category = await prisma.category.findFirst({
        where: { id: updateData.categoryId, companyId },
      })

      if (!category) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Kategori bulunamadı',
        })
      }
    }

    // Vergi kontrolü
    if (updateData.taxId) {
      const tax = await prisma.tax.findFirst({
        where: { id: updateData.taxId, companyId },
      })

      if (!tax) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Vergi oranı bulunamadı',
        })
      }
    }

    // Decimal dönüşümleri
    const processedUpdateData = {
      ...updateData,
      ...(updateData.basePrice && { basePrice: new Decimal(updateData.basePrice) }),
      ...(updateData.criticalStock && { criticalStock: new Decimal(updateData.criticalStock) }),
    }

    // Ürün güncelle
    await prisma.product.update({
      where: { id },
      data: processedUpdateData,
    })

    return true
  }

  /**
   * Ürün siler
   * @param id - Ürün ID'si
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async deleteProduct(id: string, companyId: string): Promise<boolean> {
    // Ürün varlık kontrolü
    const product = await prisma.product.findFirst({
      where: { id, companyId },
      include: {
        _count: {
          select: {
            variants: true,
            orderItems: true,
            stockMovements: true,
          },
        },
      },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Sipariş kontrolü
    if (product._count.orderItems > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Siparişlerde kullanılan ürün silinemez',
      })
    }

    // Stok hareketi kontrolü
    if (product._count.stockMovements > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Stok hareketi olan ürün silinemez',
      })
    }

    // Ürün sil (Cascade ile varyantlar ve modifiyer ilişkileri de silinir)
    await prisma.product.delete({
      where: { id },
    })

    return true
  }



  // ==================== PRODUCT MODIFIER RELATION METHODS ====================

  /**
   * Ürüne modifiyer grubu ekler
   * @param input - Ürün-modifiyer grubu ilişki bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async addModifierGroupToProduct(
    input: ProductAddModifierGroupInput,
    companyId: string
  ): Promise<boolean> {
    const { productId, modifierGroupId, displayOrder } = input

    // Ürün kontrolü
    const product = await prisma.product.findFirst({
      where: { id: productId, companyId },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Modifiyer grubu kontrolü
    const modifierGroup = await prisma.modifierGroup.findUnique({
      where: { id: modifierGroupId },
    })

    if (!modifierGroup) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer grubu bulunamadı',
      })
    }

    // Mevcut ilişki kontrolü
    const existingRelation = await prisma.productModifierGroup.findUnique({
      where: {
        productId_modifierGroupId: {
          productId,
          modifierGroupId,
        },
      },
    })

    if (existingRelation) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu modifiyer grubu zaten ürüne eklenmiş',
      })
    }

    // İlişki oluştur
    await prisma.productModifierGroup.create({
      data: {
        productId,
        modifierGroupId,
        displayOrder,
      },
    })

    // Ürünün hasModifiers özelliğini güncelle
    await prisma.product.update({
      where: { id: productId },
      data: { hasModifiers: true },
    })

    return true
  }

  /**
   * Üründen modifiyer grubu kaldırır
   * @param input - Ürün-modifiyer grubu ilişki bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async removeModifierGroupFromProduct(
    input: ProductRemoveModifierGroupInput,
    companyId: string
  ): Promise<boolean> {
    const { productId, modifierGroupId } = input

    // Ürün kontrolü
    const product = await prisma.product.findFirst({
      where: { id: productId, companyId },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // İlişki kontrolü
    const existingRelation = await prisma.productModifierGroup.findUnique({
      where: {
        productId_modifierGroupId: {
          productId,
          modifierGroupId,
        },
      },
    })

    if (!existingRelation) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Bu modifiyer grubu ürüne eklenmemiş',
      })
    }

    // İlişki sil
    await prisma.productModifierGroup.delete({
      where: {
        productId_modifierGroupId: {
          productId,
          modifierGroupId,
        },
      },
    })

    // Ürünün kalan modifiyer grubu sayısını kontrol et
    const remainingModifierGroups = await prisma.productModifierGroup.count({
      where: { productId },
    })

    // Eğer modifiyer grubu kalmadıysa hasModifiers'ı false yap
    if (remainingModifierGroups === 0) {
      await prisma.product.update({
        where: { id: productId },
        data: { hasModifiers: false },
      })
    }

    return true
  }


}
