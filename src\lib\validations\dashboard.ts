import { z } from 'zod'
import { OrderStatus, PaymentStatus } from '@prisma/client'

// ==================== INPUT SCHEMAS ====================

/**
 * Dashboard genel filtre şeması
 */
export const dashboardFilterSchema = z.object({
  branchId: z
    .string()
    .cuid('Geçerli bir şube ID\'si gerekli')
    .optional(),
  date: z
    .date()
    .optional()
    .default(() => new Date()),
})

/**
 * Özet istatistikler için giriş şeması
 */
export const overviewStatsInputSchema = dashboardFilterSchema

/**
 * Popüler yemekler için giriş şeması
 */
export const popularDishesInputSchema = z.object({
  branchId: z
    .string()
    .cuid('Geçerli bir şube ID\'si gerekli')
    .optional(),
  startDate: z
    .date()
    .default(() => {
      const date = new Date()
      date.setHours(0, 0, 0, 0)
      return date
    }),
  endDate: z
    .date()
    .default(() => {
      const date = new Date()
      date.setHours(23, 59, 59, 999)
      return date
    }),
  limit: z
    .number()
    .min(1, 'Limit en az 1 olmalıdır')
    .max(20, 'Limit en fazla 20 olabilir')
    .default(5),
})

/**
 * Stok kritik ürünler için giriş şeması
 */
export const outOfStockInputSchema = z.object({
  branchId: z
    .string()
    .cuid('Geçerli bir şube ID\'si gerekli')
    .optional(),
  limit: z
    .number()
    .min(1, 'Limit en az 1 olmalıdır')
    .max(20, 'Limit en fazla 20 olabilir')
    .default(5),
})

/**
 * Sipariş listesi için giriş şeması
 */
export const ordersListInputSchema = z.object({
  branchId: z
    .string()
    .cuid('Geçerli bir şube ID\'si gerekli')
    .optional(),
  status: z
    .enum(['inProgress', 'waitingForPayment'])
    .optional(),
  search: z
    .string()
    .max(100, 'Arama terimi en fazla 100 karakter olabilir')
    .optional(),
  limit: z
    .number()
    .min(1, 'Limit en az 1 olmalıdır')
    .max(50, 'Limit en fazla 50 olabilir')
    .default(10),
})

// ==================== OUTPUT SCHEMAS ====================

/**
 * Yüzde değişim şeması
 */
export const percentageChangeSchema = z.object({
  value: z.string(), // "+3.2%" veya "-1.5%" formatında
  isPositive: z.boolean(),
})

/**
 * Özet istatistikler çıkış şeması
 */
export const overviewStatsOutputSchema = z.object({
  totalEarning: z.object({
    amount: z.string(), // Decimal string formatında
    change: percentageChangeSchema,
  }),
  inProgressOrdersCount: z.object({
    count: z.number(),
    change: percentageChangeSchema,
  }),
  waitingListCount: z.object({
    count: z.number(),
    change: percentageChangeSchema,
  }),
})

/**
 * Popüler yemek öğesi şeması
 */
export const popularDishItemSchema = z.object({
  productId: z.string().cuid(),
  name: z.string(),
  orderCount: z.number(),
  image: z.string().optional(),
})

/**
 * Popüler yemekler çıkış şeması
 */
export const popularDishesOutputSchema = z.array(popularDishItemSchema)

/**
 * Stok kritik ürün öğesi şeması
 */
export const outOfStockItemSchema = z.object({
  productId: z.string().cuid(),
  name: z.string(),
  currentStock: z.string(), // Decimal string formatında
  unit: z.string(),
  criticalLevel: z.string().optional(), // Decimal string formatında
  availableInfo: z.string().optional(), // "Available: Tomorrow" gibi
  image: z.string().optional(),
})

/**
 * Stok kritik ürünler çıkış şeması
 */
export const outOfStockOutputSchema = z.array(outOfStockItemSchema)

/**
 * Sipariş listesi öğesi şeması
 */
export const orderListItemSchema = z.object({
  orderId: z.string().cuid(),
  orderNumber: z.string(), // "A9", "A13" gibi masa numarası veya sipariş numarası
  customerName: z.string().optional(),
  totalItems: z.number(),
  status: z.string(), // "Ready", "Cooking Now", "In the Kitchen", "Pay Now" gibi
  statusColor: z.enum(['green', 'orange', 'blue', 'red']), // UI için renk kodu
  tableNumber: z.string().optional(),
  hasPayButton: z.boolean().default(false), // "Pay Now" butonu gösterilecek mi?
})

/**
 * Sipariş listesi çıkış şeması
 */
export const ordersListOutputSchema = z.array(orderListItemSchema)

// ==================== TYPE EXPORTS ====================

export type DashboardFilterInput = z.infer<typeof dashboardFilterSchema>
export type OverviewStatsInput = z.infer<typeof overviewStatsInputSchema>
export type PopularDishesInput = z.infer<typeof popularDishesInputSchema>
export type OutOfStockInput = z.infer<typeof outOfStockInputSchema>
export type OrdersListInput = z.infer<typeof ordersListInputSchema>

export type PercentageChange = z.infer<typeof percentageChangeSchema>
export type OverviewStatsOutput = z.infer<typeof overviewStatsOutputSchema>
export type PopularDishItem = z.infer<typeof popularDishItemSchema>
export type PopularDishesOutput = z.infer<typeof popularDishesOutputSchema>
export type OutOfStockItem = z.infer<typeof outOfStockItemSchema>
export type OutOfStockOutput = z.infer<typeof outOfStockOutputSchema>
export type OrderListItem = z.infer<typeof orderListItemSchema>
export type OrdersListOutput = z.infer<typeof ordersListOutputSchema>
