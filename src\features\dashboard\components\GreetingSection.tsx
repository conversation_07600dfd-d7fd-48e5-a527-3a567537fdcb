import { useAuthStore } from "../../auth/stores/authStore"
import { useState, useEffect } from "react"

/**
 * Ka<PERSON><PERSON><PERSON><PERSON>a bölümü bileşeni - Figma tasarımına uygun layout
 *
 * Bu bileşen:
 * - Sol: Kullanıcıya kişiselleştirilmiş karşılama mesajı
 * - Orta: Büyük saat ve tarih (Figma'daki gibi)
 * - Sağ: Sipariş durumları (In Progress, Waiting for Payment)
 */
export function GreetingSection() {
  const { user } = useAuthStore()
  const [currentTime, setCurrentTime] = useState(new Date())

  // Saati her saniye güncelle
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Güncel tarih ve saat
  const timeString = currentTime.toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  const dateString = currentTime.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // Günün saatine göre karşılama
  const hour = currentTime.getHours()
  const greeting = hour < 12 ? 'Günaydın'
    : hour < 17 ? 'İyi Öğleden Sonra'
    : 'İyi Akşamlar'

  const firstName = user?.firstName || 'Kullanıcı'

  return (
    <div className="flex justify-between items-center">
      {/* Sol: Karşılama Mesajı */}
      <div className="flex flex-col gap-1">
        <h1 className="text-[16px] font-semibold text-[#202325]">
          {greeting}, {firstName}
        </h1>
        <div className="flex items-center gap-1">
          <span className="text-[14px] text-[#636566]">
            Müşterilerinize en iyi hizmeti verin
          </span>
          <span className="text-[15px]">😊</span>
        </div>
      </div>

      {/* Sağ: Saat ve Tarih */}
      <div className="text-right">
        <div className="text-[24px] font-medium text-[#202325]">
          {timeString}
        </div>
        <div className="text-[12px] text-[#636566]">{dateString}</div>
      </div>
    </div>
  )
}
