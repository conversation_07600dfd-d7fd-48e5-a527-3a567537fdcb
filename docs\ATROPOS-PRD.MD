# ATROPOS PRD (Product Requirements Document)

## 1. <PERSON><PERSON><PERSON><PERSON>

### 1.1 Vizyon
Türkiye'deki restoran ve kafe işletmeleri için modern, hızlı ve güvenilir bir masaüstü POS (Point of Sale) sistemi geliştirmek. Sistem, çoklu şube yönetimi, offline çalışabilme ve Türkiye'ye özel mali düzenlemelere uyum sağlama özelliklerine sahip olacak.

### 1.2 <PERSON><PERSON><PERSON>
- Restoran ve kafe sahipleri
- Kasa görevlileri
- Garsonlar
- Mutfak personeli
- Muhasebe personeli
- Şube müdürleri

### 1.3 Temel Değer Önerisi
- **Hız**: Electron tabanlı native performans
- **Güvenilirlik**: Offline çalışabilme, veri senkronizasyonu
- **Uyumluluk**: Türk mali mevzuatına tam uyum
- **Kullanım Kolaylığı**: Modern UI/UX, çoklu dil desteği

## 2. Teknik Gereksinimler

### 2.1 Teknoloji Stack
- **Frontend**: Electron + React + TypeScript
- **UI Framework**: Tailwind CSS v3 + shadcn/ui
- **Database**: PostgreSQL + Prisma ORM
- **State Management**: Zustand
- **API Communication**: tRPC veya REST
- **Real-time Sync**: Socket.io

### 2.2 Sistem Gereksinimleri
- **İşletim Sistemi**: Windows 10+ (öncelik), macOS 10.14+, Ubuntu 20.04+
- **RAM**: Minimum 4GB, Önerilen 8GB
- **Depolama**: Minimum 2GB boş alan
- **İnternet**: Senkronizasyon için gerekli, offline çalışabilir

## 3. Fonksiyonel Gereksinimler

### 3.1 Kullanıcı Yönetimi

#### 3.1.1 Roller ve Yetkilendirme
- **Süper Admin**: Tüm şubelere erişim, sistem ayarları
- **Şube Müdürü**: Şube bazlı tüm yetkiler
- **Kasa Görevlisi**: Satış, iade, kasa işlemleri
- **Garson**: Sipariş alma, masa yönetimi
- **Mutfak**: Sipariş görüntüleme, hazırlık durumu

#### 3.1.2 Kimlik Doğrulama
- Kullanıcı adı/şifre ile giriş
- PIN kodu ile hızlı giriş
- Oturum zaman aşımı
- Yetki bazlı menü görünürlüğü

### 3.2 Ürün Yönetimi

#### 3.2.1 Ürün Kategorileri
- Hiyerarşik kategori yapısı (Ana kategori > Alt kategori)
- Kategori ikonu ve renk desteği
- Kategori bazlı KDV oranları

#### 3.2.2 Ürün Özellikleri
- Ürün adı, açıklama, fotoğraf
- Çoklu fiyat seçeneği (küçük, orta, büyük)
- Porsiyon ayarları (tam, yarım, çeyrek)
- Modifiyer grupları (sos seçimi, pişirme derecesi vb.)
- Stok takibi (opsiyonel)
- Reçete yönetimi
- Allerjen bilgileri

#### 3.2.3 Fiyatlandırma
- KDV dahil/hariç fiyat girişi
- Şube bazlı fiyatlandırma
- Happy hour / zaman bazlı fiyatlandırma
- İndirim kuralları

### 3.3 Satış İşlemleri

#### 3.3.1 Sipariş Alma
- Hızlı ürün arama (barkod, isim, kısayol)
- Favori ürünler
- Ürün modifikasyonları
- Sipariş notları
- Müşteri bilgisi ekleme

#### 3.3.2 Masa Yönetimi
- Salon düzeni editörü
- Masa durumları (boş, dolu, rezerve, hesap bekliyor)
- Masa birleştirme/ayırma
- Hesap transfer
- Masa bazlı sipariş takibi

#### 3.3.3 Ödeme İşlemleri
- Nakit
- Kredi/Banka kartı (POS cihaz entegrasyonu)
- Çoklu ödeme yöntemi
- Kısmi ödeme
- Müşteri borç takibi
- Bahşiş yönetimi

#### 3.3.4 Fatura ve Fiş
- e-Arşiv fatura
- Mali müşteri fişi
- Adisyon yazdırma
- QR kodlu fiş (GİB uyumlu)

### 3.4 Mutfak Yönetimi

#### 3.4.1 Mutfak Ekranı
- Gelen siparişler listesi
- Hazırlık süresi takibi
- Sipariş önceliklendirme
- Hazır bildirimi

#### 3.4.2 Yazıcı Yönetimi
- Kategori bazlı yazıcı yönlendirme
- Mutfak/bar yazıcıları
- Adisyon yazıcısı

### 3.5 Raporlama

#### 3.5.1 Satış Raporları
- Günlük/haftalık/aylık satış özeti
- Ürün bazlı satış raporu
- Kategori performansı
- Saatlik satış dağılımı
- Garson performansı

#### 3.5.2 Mali Raporlar
- Kasa raporu (X ve Z raporu)
- KDV raporu
- Gelir/gider takibi
- Kar/zarar analizi

#### 3.5.3 Stok Raporları
- Güncel stok durumu
- Stok hareketleri
- Kritik stok uyarıları
- Fire/zayi raporu

### 3.6 Entegrasyonlar

#### 3.6.1 Mali Entegrasyonlar
- GİB e-Arşiv entegrasyonu
- ÖKC (Ödeme Kaydedici Cihaz) entegrasyonu
- Muhasebe yazılımları (Logo, Mikro, vb.)

#### 3.6.2 Ödeme Sistemleri
- Sanal POS entegrasyonları
- QR kod ile ödeme (Troy, Paycell, vb.)

#### 3.6.3 Online Platform Entegrasyonları
- Yemeksepeti
- Getir Yemek
- Trendyol Yemek

### 3.7 Sistem Yönetimi

#### 3.7.1 Şube Yönetimi
- Merkezi ürün yönetimi
- Şube bazlı ayarlar
- Veri senkronizasyonu

#### 3.7.2 Yedekleme
- Otomatik local yedekleme
- Cloud yedekleme (opsiyonel)
- Yedekten geri yükleme

#### 3.7.3 Güncelleme
- Otomatik güncelleme kontrolü
- Güncelleme bildirimleri
- Rollback mekanizması

## 4. Kullanıcı Arayüzü Gereksinimleri

### 4.1 Genel Prensipler
- Touch-friendly büyük butonlar
- Karanlık/aydınlık tema desteği
- Responsive tasarım (farklı ekran boyutları)
- Hızlı erişim kısayolları
- Klavye kısayol desteği

### 4.2 Ekran Tasarımları
- **Ana Satış Ekranı**: Grid yapıda kategori/ürün listesi
- **Masa Planı**: Drag & drop salon düzeni
- **Kasa Ekranı**: Ödeme yöntemleri, hesap makinesi
- **Raporlar**: Grafikli dashboard
- **Ayarlar**: Tab yapısında organize menüler

### 4.3 Çoklu Dil Desteği
- Türkçe (varsayılan)
- İngilizce
- Arapça (RTL desteği)
- Dil dosyaları için JSON yapısı

## 5. Güvenlik Gereksinimleri

### 5.1 Veri Güvenliği
- Şifreler bcrypt ile hashlenmiş
- API iletişimi HTTPS üzerinden
- Hassas veriler için encryption

### 5.2 Yetkilendirme
- Role-based access control (RBAC)
- API endpoint koruması
- İşlem logları

### 5.3 PCI DSS Uyumluluğu
- Kart bilgilerini saklamama
- Güvenli POS entegrasyonu

## 6. Performans Gereksinimleri

- Ürün arama: < 100ms
- Sipariş kaydetme: < 500ms
- Ekran geçişleri: < 200ms
- Başlangıç süresi: < 5 saniye
- Offline'dan online'a geçiş: < 10 saniye

## 7. Milestone ve Zaman Planı

### Faz 1: Temel Fonksiyonlar (2 ay)
- Kullanıcı yönetimi
- Ürün yönetimi
- Basit satış ekranı
- Temel raporlar

### Faz 2: Gelişmiş Özellikler (2 ay)
- Masa yönetimi
- Mutfak ekranı
- Entegrasyonlar
- Detaylı raporlar

### Faz 3: Optimizasyon ve Polish (1 ay)
- Performance tuning
- UI/UX iyileştirmeleri
- Beta test
- Bug fixing

### Faz 4: Launch (1 ay)
- Deployment hazırlıkları
- Dokümantasyon
- Eğitim materyalleri
- Go-live

## 8. Başarı Kriterleri

- Saniyede 10 sipariş işleyebilme
- %99.9 uptime (offline hariç)
- Ortalama işlem süresi < 30 saniye
- Kullanıcı memnuniyeti > %90
- 0 kritik güvenlik açığı

## 9. Riskler ve Çözümler

### Risk 1: İnternet Kesintileri
**Çözüm**: Robust offline mode, otomatik senkronizasyon

### Risk 2: Veri Kaybı
**Çözüm**: Çoklu yedekleme stratejisi, transaction log

### Risk 3: Mali Mevzuat Değişiklikleri
**Çözüm**: Modüler yapı, hızlı güncelleme mekanizması

### Risk 4: Donanım Uyumsuzlukları
**Çözüm**: Geniş yazıcı/POS cihaz desteği, driver yönetimi

## 10. Ekler

### 10.1 Terimler Sözlüğü
- **Adisyon**: Masa hesabı
- **Fire**: İstenmeyen ürün kaybı
- **Modifiyer**: Ürün özelleştirme seçeneği
- **Z Raporu**: Günlük kasa kapanış raporu

### 10.2 Rekabet Analizi
- Adisyon
- Menpos
- Netsis
- Payfour

### 10.3 Yasal Gereklilikler
- GİB e-Arşiv zorunluluğu
- KDV oranları güncelliği
- Mali müşteri bilgi saklama süreleri