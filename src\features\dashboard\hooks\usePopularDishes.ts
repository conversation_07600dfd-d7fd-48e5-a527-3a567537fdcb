import { trpc } from '../../../lib/trpc-client'
import type { PopularDishesInput } from '../types'

/**
 * En çok satan ürünleri getiren hook
 * 
 * Bu hook:
 * - Belirtilen tarih aralığında en çok satan ürünleri listeler
 * - <PERSON><PERSON><PERSON><PERSON> adı, satış miktarı ve görsel bilgilerini döndürür
 * - Şube filtrelemesi yapar
 * - Limit parametresi ile sonuç sayısını sınırlar
 */
export function usePopularDishes(input?: PopularDishesInput) {
  return trpc.dashboard.getPopularDishes.useQuery(
    input || { limit: 5 },
    {
      staleTime: 5 * 60 * 1000, // 5 dakika
      refetchInterval: 2 * 60 * 1000, // 2 dakikada bir otomatik güncelle
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    }
  )
}
