import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type { 
  ModifierGroupCreateInput,
  ModifierGroupUpdateInput,
  ModifierGroupOutput,
  ModifierGroupListOutput,
  ModifierCreateInput,
  ModifierUpdateInput,
  ModifierOutput
} from '../../lib/validations/products'

/**
 * ModifierService - Modifiyer yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Modifiyer grubu CRUD işlemleri
 * - Modifiyer CRUD işlemleri
 * - Modifiyer grup-ürün ilişki yönetimi
 * - Decimal.js ile güvenli para hesaplamaları
 */
export class ModifierService {
  // ==================== MODIFIER GROUP METHODS ====================

  /**
   * Modifiyer grubu listesini getirir
   * @param page - Sayfa numarası
   * @param limit - Sayfa başına kayıt sayısı
   * @param includeModifiers - Modifiyerleri dahil et
   * @returns Modifiyer grubu listesi
   */
  static async getModifierGroups(
    page = 1,
    limit = 10,
    includeModifiers = false
  ): Promise<ModifierGroupListOutput> {
    const skip = (page - 1) * limit

    // Include koşulları
    const include = {
      _count: {
        select: {
          modifiers: true,
          products: true,
        },
      },
      ...(includeModifiers && {
        modifiers: {
          where: { active: true },
          orderBy: { displayOrder: 'asc' as const },
          select: {
            id: true,
            name: true,
            price: true,
            displayOrder: true,
            active: true,
          },
        },
      }),
    }

    // Modifiyer gruplarını getir
    const [modifierGroups, total] = await Promise.all([
      prisma.modifierGroup.findMany({
        include,
        orderBy: { displayOrder: 'asc' },
        skip: limit ? skip : undefined,
        take: limit || undefined,
      }),
      prisma.modifierGroup.count(),
    ])

    const totalPages = limit ? Math.ceil(total / limit) : 1

    // Decimal değerleri string'e çevir
    const formattedModifierGroups = modifierGroups.map(group => ({
      ...group,
      modifiers: group.modifiers?.map(modifier => ({
        ...modifier,
        price: modifier.price.toString(),
      })),
    }))

    return {
      modifierGroups: formattedModifierGroups as ModifierGroupOutput[],
      total,
      page,
      limit: limit || total,
      totalPages,
    }
  }

  /**
   * ID'ye göre modifiyer grubu getirir
   * @param id - Modifiyer grubu ID'si
   * @param includeModifiers - Modifiyerleri dahil et
   * @returns Modifiyer grubu bilgisi
   */
  static async getModifierGroupById(
    id: string,
    includeModifiers = true
  ): Promise<ModifierGroupOutput> {
    const modifierGroup = await prisma.modifierGroup.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            modifiers: true,
            products: true,
          },
        },
        ...(includeModifiers && {
          modifiers: {
            where: { active: true },
            orderBy: { displayOrder: 'asc' },
            select: {
              id: true,
              name: true,
              price: true,
              displayOrder: true,
              active: true,
            },
          },
        }),
      },
    })

    if (!modifierGroup) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer grubu bulunamadı',
      })
    }

    // Decimal değerleri string'e çevir
    const formattedModifierGroup = {
      ...modifierGroup,
      modifiers: modifierGroup.modifiers?.map(modifier => ({
        ...modifier,
        price: modifier.price.toString(),
      })),
    }

    return formattedModifierGroup as ModifierGroupOutput
  }

  /**
   * Yeni modifiyer grubu oluşturur
   * @param input - Modifiyer grubu bilgileri
   * @returns Oluşturulan modifiyer grubu ID'si
   */
  static async createModifierGroup(input: ModifierGroupCreateInput): Promise<string> {
    const {
      name,
      minSelection,
      maxSelection,
      required,
      displayOrder,
      active,
    } = input

    // Aynı isimde modifiyer grubu kontrolü
    const existingGroup = await prisma.modifierGroup.findFirst({
      where: { name },
    })

    if (existingGroup) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu isimde bir modifiyer grubu zaten mevcut',
      })
    }

    // Seçim sayısı kontrolü
    if (minSelection > maxSelection) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Minimum seçim sayısı maksimum seçim sayısından büyük olamaz',
      })
    }

    // Modifiyer grubu oluştur
    const modifierGroup = await prisma.modifierGroup.create({
      data: {
        name,
        minSelection,
        maxSelection,
        required,
        displayOrder,
        active,
      },
    })

    return modifierGroup.id
  }

  /**
   * Modifiyer grubu günceller
   * @param input - Güncellenecek modifiyer grubu bilgileri
   * @returns Başarı durumu
   */
  static async updateModifierGroup(input: ModifierGroupUpdateInput): Promise<boolean> {
    const { id, ...updateData } = input

    // Modifiyer grubu varlık kontrolü
    const existingGroup = await prisma.modifierGroup.findUnique({
      where: { id },
    })

    if (!existingGroup) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer grubu bulunamadı',
      })
    }

    // İsim değişikliği kontrolü
    if (updateData.name && updateData.name !== existingGroup.name) {
      const duplicateGroup = await prisma.modifierGroup.findFirst({
        where: {
          name: updateData.name,
          id: { not: id },
        },
      })

      if (duplicateGroup) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu isimde bir modifiyer grubu zaten mevcut',
        })
      }
    }

    // Seçim sayısı kontrolü
    const minSelection = updateData.minSelection ?? existingGroup.minSelection
    const maxSelection = updateData.maxSelection ?? existingGroup.maxSelection

    if (minSelection > maxSelection) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Minimum seçim sayısı maksimum seçim sayısından büyük olamaz',
      })
    }

    // Modifiyer grubu güncelle
    await prisma.modifierGroup.update({
      where: { id },
      data: updateData,
    })

    return true
  }

  /**
   * Modifiyer grubu siler
   * @param id - Modifiyer grubu ID'si
   * @returns Başarı durumu
   */
  static async deleteModifierGroup(id: string): Promise<boolean> {
    // Modifiyer grubu varlık kontrolü
    const modifierGroup = await prisma.modifierGroup.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            modifiers: true,
            products: true,
          },
        },
      },
    })

    if (!modifierGroup) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer grubu bulunamadı',
      })
    }

    // Ürün kontrolü
    if (modifierGroup._count.products > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Ürünlerde kullanılan modifiyer grubu silinemez',
      })
    }

    // Modifiyer grubu sil (Cascade ile modifiyerler de silinir)
    await prisma.modifierGroup.delete({
      where: { id },
    })

    return true
  }

  // ==================== MODIFIER METHODS ====================

  /**
   * Modifiyer oluşturur
   * @param input - Modifiyer bilgileri
   * @returns Oluşturulan modifiyer ID'si
   */
  static async createModifier(input: ModifierCreateInput): Promise<string> {
    const { groupId, name, price, displayOrder, active } = input

    // Modifiyer grubu kontrolü
    const modifierGroup = await prisma.modifierGroup.findUnique({
      where: { id: groupId },
    })

    if (!modifierGroup) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer grubu bulunamadı',
      })
    }

    // Aynı isimde modifiyer kontrolü
    const existingModifier = await prisma.modifier.findFirst({
      where: {
        groupId,
        name,
      },
    })

    if (existingModifier) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu isimde bir modifiyer zaten mevcut',
      })
    }

    // Modifiyer oluştur
    const modifier = await prisma.modifier.create({
      data: {
        groupId,
        name,
        price: new Decimal(price),
        displayOrder,
        active,
      },
    })

    return modifier.id
  }

  /**
   * Modifiyer günceller
   * @param input - Güncellenecek modifiyer bilgileri
   * @returns Başarı durumu
   */
  static async updateModifier(input: ModifierUpdateInput): Promise<boolean> {
    const { id, ...updateData } = input

    // Modifiyer varlık kontrolü
    const existingModifier = await prisma.modifier.findUnique({
      where: { id },
    })

    if (!existingModifier) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer bulunamadı',
      })
    }

    // İsim değişikliği kontrolü
    if (updateData.name && updateData.name !== existingModifier.name) {
      const duplicateModifier = await prisma.modifier.findFirst({
        where: {
          groupId: existingModifier.groupId,
          name: updateData.name,
          id: { not: id },
        },
      })

      if (duplicateModifier) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu isimde bir modifiyer zaten mevcut',
        })
      }
    }

    // Decimal dönüşümü
    const processedUpdateData = {
      ...updateData,
      ...(updateData.price && { price: new Decimal(updateData.price) }),
    }

    // Modifiyer güncelle
    await prisma.modifier.update({
      where: { id },
      data: processedUpdateData,
    })

    return true
  }

  /**
   * Modifiyer siler
   * @param id - Modifiyer ID'si
   * @returns Başarı durumu
   */
  static async deleteModifier(id: string): Promise<boolean> {
    // Modifiyer varlık kontrolü
    const modifier = await prisma.modifier.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orderItemModifiers: true,
          },
        },
      },
    })

    if (!modifier) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Modifiyer bulunamadı',
      })
    }

    // Sipariş kontrolü
    if (modifier._count.orderItemModifiers > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Siparişlerde kullanılan modifiyer silinemez',
      })
    }

    // Modifiyer sil
    await prisma.modifier.delete({
      where: { id },
    })

    return true
  }
}
