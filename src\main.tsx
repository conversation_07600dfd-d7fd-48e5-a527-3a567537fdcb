import React from 'react'
import <PERSON>act<PERSON><PERSON> from 'react-dom/client'
import App from './App'
import './App.css'

console.log('main.tsx loading...')
console.log('React:', React)
console.log('ReactDOM:', ReactDOM)

const rootElement = document.getElementById('root')
console.log('Root element:', rootElement)

if (!rootElement) {
  console.error('Root element not found!')
  document.body.innerHTML = '<div style="padding: 20px; color: red;">Root element not found!</div>'
} else {
  try {
    const root = ReactDOM.createRoot(rootElement)
    console.log('React root created:', root)

    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    )
    console.log('App rendered successfully')
  } catch (error) {
    console.error('Render error:', error)
    document.body.innerHTML = `<div style="padding: 20px; color: red;">Render error: ${error}</div>`
  }
}

