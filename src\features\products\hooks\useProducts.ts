import React from 'react'
import { trpc } from '../../../lib/trpc-client'
import { toast } from 'sonner'
import type {
  ProductListInput,
  ProductCreateInput,
  ProductUpdateInput,
} from '../../../lib/validations/products'

/**
 * <PERSON>rün yönetimi için TanStack Query hooks
 * 
 * Bu hooks:
 * - Ürün listesi getirme (filtreleme, arama, sayfalama)
 * - Ürün detayı getirme
 * - <PERSON>r<PERSON>n oluşturma, güncelleme, silme
 * - Optimistic updates
 * - Error handling ve toast bildirimleri
 * - Cache invalidation
 */

/**
 * Ürün listesi getiren hook
 */
export function useProducts(input?: ProductListInput) {
  return trpc.products.product.list.useQuery(
    input || { page: 1, limit: 10 },
    {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error) => {
        console.error('Ürün listesi getirme hatası:', error)
        toast.error('Ürün listesi yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Tek ürün detayı getiren hook
 */
export function useProduct(productId: string) {
  return trpc.products.product.getById.useQuery(
    { id: productId },
    {
      enabled: !!productId,
      staleTime: 5 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Ürün detayı getirme hatası:', error)
        toast.error('Ürün detayları yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Ürün oluşturma hook'u
 */
export function useCreateProduct() {
  const utils = trpc.useUtils()

  return trpc.products.product.create.useMutation({
    onSuccess: (data) => {
      toast.success('Ürün başarıyla oluşturuldu')
      
      // Cache'i invalidate et
      utils.products.product.list.invalidate()
    },
    onError: (error) => {
      console.error('Ürün oluşturma hatası:', error)
      
      // Hata mesajını kullanıcı dostu hale getir
      const errorMessage = error.message || 'Ürün oluşturulurken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Ürün güncelleme hook'u
 */
export function useUpdateProduct() {
  const utils = trpc.useUtils()

  return trpc.products.product.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Ürün başarıyla güncellendi')
      
      // İlgili cache'leri invalidate et
      utils.products.product.list.invalidate()
      utils.products.product.getById.invalidate({ id: variables.id })
    },
    onError: (error) => {
      console.error('Ürün güncelleme hatası:', error)
      
      const errorMessage = error.message || 'Ürün güncellenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Ürün silme hook'u
 */
export function useDeleteProduct() {
  const utils = trpc.useUtils()

  return trpc.products.product.delete.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Ürün başarıyla silindi')
      
      // Cache'i invalidate et
      utils.products.product.list.invalidate()
      
      // Silinen ürünün cache'ini temizle
      utils.products.product.getById.removeQueries({ id: variables.id })
    },
    onError: (error) => {
      console.error('Ürün silme hatası:', error)
      
      const errorMessage = error.message || 'Ürün silinirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Ürüne modifiyer grubu ekleme hook'u
 */
export function useAddModifierGroupToProduct() {
  const utils = trpc.useUtils()

  return trpc.products.product.addModifierGroup.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Modifiyer grubu ürüne başarıyla eklendi')
      
      // İlgili cache'leri invalidate et
      utils.products.product.list.invalidate()
      utils.products.product.getById.invalidate({ id: variables.productId })
    },
    onError: (error) => {
      console.error('Modifiyer grubu ekleme hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer grubu eklenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Üründen modifiyer grubu kaldırma hook'u
 */
export function useRemoveModifierGroupFromProduct() {
  const utils = trpc.useUtils()

  return trpc.products.product.removeModifierGroup.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Modifiyer grubu üründen başarıyla kaldırıldı')
      
      // İlgili cache'leri invalidate et
      utils.products.product.list.invalidate()
      utils.products.product.getById.invalidate({ id: variables.productId })
    },
    onError: (error) => {
      console.error('Modifiyer grubu kaldırma hatası:', error)
      
      const errorMessage = error.message || 'Modifiyer grubu kaldırılırken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Ürün listesi için arama hook'u (debounced)
 */
export function useProductSearch(searchQuery: string, delay: number = 300) {
  const [debouncedQuery, setDebouncedQuery] = React.useState(searchQuery)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
    }, delay)

    return () => clearTimeout(timer)
  }, [searchQuery, delay])

  return useProducts({
    search: debouncedQuery || undefined,
    page: 1,
    limit: 20
  })
}

/**
 * Kategori bazlı ürün listesi hook'u
 */
export function useProductsByCategory(categoryId: string) {
  return trpc.products.product.list.useQuery(
    {
      categoryId,
      page: 1,
      limit: 50,
      includeVariants: true
    },
    {
      enabled: !!categoryId,
      staleTime: 3 * 60 * 1000, // 3 dakika
      retry: 3,
      onError: (error) => {
        console.error('Kategori ürünleri getirme hatası:', error)
        toast.error('Kategori ürünleri yüklenirken bir hata oluştu')
      }
    }
  )
}
