// Uygulama sabitleri
export const APP_CONFIG = {
  name: 'Restaurant POS',
  version: '1.0.0',
  description: 'Modern Restaurant Point of Sale System',
} as const

// API sabitleri
export const API_CONFIG = {
  baseUrl: process.env.NODE_ENV === 'production' 
    ? 'https://api.restaurant-pos.com' 
    : 'http://localhost:3001',
  timeout: 30000,
  retryAttempts: 3,
} as const

// Sayfalama sabitleri
export const PAGINATION = {
  defaultLimit: 10,
  maxLimit: 100,
  defaultPage: 1,
} as const

// Kullanıcı rolleri
export const USER_ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  BRANCH_MANAGER: 'BRANCH_MANAGER',
  CASHIER: 'CASHIER',
  WAITER: 'WAITER',
  KITCHEN: 'KITCHEN',
  REPORTER: 'REPORTER',
} as const

// Sipariş durumları
export const ORDER_STATUS = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PREPARING: 'PREPARING',
  READY: 'READY',
  DELIVERING: 'DELIVERING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
} as const

// Ödeme durumları
export const PAYMENT_STATUS = {
  UNPAID: 'UNPAID',
  PARTIAL: 'PARTIAL',
  PAID: 'PAID',
  REFUNDED: 'REFUNDED',
} as const

// Sipariş tipleri
export const ORDER_TYPES = {
  DINE_IN: 'DINE_IN',
  TAKEAWAY: 'TAKEAWAY',
  DELIVERY: 'DELIVERY',
  ONLINE: 'ONLINE',
} as const

// Masa durumları
export const TABLE_STATUS = {
  EMPTY: 'EMPTY',
  OCCUPIED: 'OCCUPIED',
  RESERVED: 'RESERVED',
  CLEANING: 'CLEANING',
  UNAVAILABLE: 'UNAVAILABLE',
} as const

// Ürün birimleri
export const PRODUCT_UNITS = {
  PIECE: 'PIECE',
  KG: 'KG',
  GRAM: 'GRAM',
  LITER: 'LITER',
  ML: 'ML',
  PORTION: 'PORTION',
} as const

// Ödeme yöntemleri
export const PAYMENT_METHODS = {
  CASH: 'CASH',
  CREDIT_CARD: 'CREDIT_CARD',
  DEBIT_CARD: 'DEBIT_CARD',
  MEAL_CARD: 'MEAL_CARD',
  MOBILE: 'MOBILE',
  TRANSFER: 'TRANSFER',
  OTHER: 'OTHER',
} as const

// Durum renkleri
export const STATUS_COLORS = {
  [ORDER_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
  [ORDER_STATUS.CONFIRMED]: 'bg-blue-100 text-blue-800',
  [ORDER_STATUS.PREPARING]: 'bg-orange-100 text-orange-800',
  [ORDER_STATUS.READY]: 'bg-green-100 text-green-800',
  [ORDER_STATUS.DELIVERING]: 'bg-purple-100 text-purple-800',
  [ORDER_STATUS.COMPLETED]: 'bg-gray-100 text-gray-800',
  [ORDER_STATUS.CANCELLED]: 'bg-red-100 text-red-800',
  
  [PAYMENT_STATUS.UNPAID]: 'bg-red-100 text-red-800',
  [PAYMENT_STATUS.PARTIAL]: 'bg-yellow-100 text-yellow-800',
  [PAYMENT_STATUS.PAID]: 'bg-green-100 text-green-800',
  [PAYMENT_STATUS.REFUNDED]: 'bg-gray-100 text-gray-800',
  
  [TABLE_STATUS.EMPTY]: 'bg-gray-100 text-gray-800',
  [TABLE_STATUS.OCCUPIED]: 'bg-red-100 text-red-800',
  [TABLE_STATUS.RESERVED]: 'bg-yellow-100 text-yellow-800',
  [TABLE_STATUS.CLEANING]: 'bg-blue-100 text-blue-800',
  [TABLE_STATUS.UNAVAILABLE]: 'bg-gray-100 text-gray-800',
} as const

// Durum etiketleri
export const STATUS_LABELS = {
  [ORDER_STATUS.PENDING]: 'Bekliyor',
  [ORDER_STATUS.CONFIRMED]: 'Onaylandı',
  [ORDER_STATUS.PREPARING]: 'Hazırlanıyor',
  [ORDER_STATUS.READY]: 'Hazır',
  [ORDER_STATUS.DELIVERING]: 'Teslimatta',
  [ORDER_STATUS.COMPLETED]: 'Tamamlandı',
  [ORDER_STATUS.CANCELLED]: 'İptal',
  
  [PAYMENT_STATUS.UNPAID]: 'Ödenmedi',
  [PAYMENT_STATUS.PARTIAL]: 'Kısmi Ödendi',
  [PAYMENT_STATUS.PAID]: 'Ödendi',
  [PAYMENT_STATUS.REFUNDED]: 'İade Edildi',
  
  [ORDER_TYPES.DINE_IN]: 'Restoranda',
  [ORDER_TYPES.TAKEAWAY]: 'Paket',
  [ORDER_TYPES.DELIVERY]: 'Eve Servis',
  [ORDER_TYPES.ONLINE]: 'Online',
  
  [TABLE_STATUS.EMPTY]: 'Boş',
  [TABLE_STATUS.OCCUPIED]: 'Dolu',
  [TABLE_STATUS.RESERVED]: 'Rezerve',
  [TABLE_STATUS.CLEANING]: 'Temizleniyor',
  [TABLE_STATUS.UNAVAILABLE]: 'Kullanılamaz',
  
  [PRODUCT_UNITS.PIECE]: 'Adet',
  [PRODUCT_UNITS.KG]: 'Kilogram',
  [PRODUCT_UNITS.GRAM]: 'Gram',
  [PRODUCT_UNITS.LITER]: 'Litre',
  [PRODUCT_UNITS.ML]: 'Mililitre',
  [PRODUCT_UNITS.PORTION]: 'Porsiyon',
  
  [USER_ROLES.SUPER_ADMIN]: 'Süper Admin',
  [USER_ROLES.ADMIN]: 'Admin',
  [USER_ROLES.BRANCH_MANAGER]: 'Şube Müdürü',
  [USER_ROLES.CASHIER]: 'Kasiyer',
  [USER_ROLES.WAITER]: 'Garson',
  [USER_ROLES.KITCHEN]: 'Mutfak',
  [USER_ROLES.REPORTER]: 'Raporlama',
} as const

// Klavye kısayolları
export const HOTKEYS = {
  NEW_ORDER: 'ctrl+n',
  SAVE_ORDER: 'ctrl+s',
  SEARCH: 'ctrl+f',
  PRINT: 'ctrl+p',
  LOGOUT: 'ctrl+shift+l',
  SETTINGS: 'ctrl+,',
  HELP: 'f1',
} as const

// Validasyon sabitleri
export const VALIDATION = {
  minPasswordLength: 6,
  maxPasswordLength: 50,
  pinLength: 4,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
  phoneRegex: /^(\+90|0)?[5][0-9]{9}$/,
  emailRegex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  taxNumberRegex: /^[0-9]{10,11}$/,
} as const

// Tema sabitleri
export const THEME = {
  colors: {
    primary: 'hsl(222.2 84% 4.9%)',
    secondary: 'hsl(210 40% 96%)',
    accent: 'hsl(210 40% 94%)',
    muted: 'hsl(210 40% 96%)',
    border: 'hsl(214.3 31.8% 91.4%)',
    background: 'hsl(0 0% 100%)',
    foreground: 'hsl(222.2 84% 4.9%)',
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
  },
} as const

