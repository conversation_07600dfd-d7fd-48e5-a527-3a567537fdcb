# PIN Login API Test Örnekleri

Bu dokümanda PIN Login backend API'lerini test etmek için gerekli <PERSON>kler bulunmaktadır.

## Gereksinimler

- Backend server'ın `http://localhost:3001` adresinde çalışıyor olması
- PostgreSQL veritabanının kurulu ve çalışır durumda olması
- Prisma migration'larının çalıştırılmış olması

## Test Verileri Hazırlama

Öncelikle test için gerekli kullanıcı verilerini oluşturun:

```sql
-- Test şirketi oluştur
INSERT INTO "Company" (id, name, "taxNumber", "taxOffice", address, phone, email, "createdAt", "updatedAt")
VALUES ('test-company-1', 'Test Restaurant', '1234567890', 'Kadıköy', 'Test Adres', '+90 212 123 45 67', '<EMAIL>', NOW(), NOW());

-- Test şubesi oluştur
INSERT INTO "Branch" (id, "companyId", code, name, address, phone, "isMainBranch", active, "createdAt", "updatedAt")
VALUES ('test-branch-1', 'test-company-1', 'MAIN', 'Ana Şube', 'Ana Şube Adresi', '+90 212 123 45 67', true, true, NOW(), NOW());

-- Test kullanıcıları oluştur (PIN'ler hashlenmiş: 1234, 5678, 9999)
INSERT INTO "User" (id, "companyId", "branchId", username, password, pin, "firstName", "lastName", role, active, "createdAt", "updatedAt")
VALUES 
  ('test-user-1', 'test-company-1', 'test-branch-1', 'elisa.klein', '$2b$10$hashedpassword1', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Elisa', 'Klein', 'CASHIER', true, NOW(), NOW()),
  ('test-user-2', 'test-company-1', 'test-branch-1', 'intan.fauziah', '$2b$10$hashedpassword2', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Intan', 'Fauziah', 'WAITER', true, NOW(), NOW()),
  ('test-user-3', 'test-company-1', 'test-branch-1', 'admin.user', '$2b$10$hashedpassword3', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'BRANCH_MANAGER', true, NOW(), NOW());
```

## API Endpoint'leri

### 1. Kullanıcı Listesi (PIN Login için)

**Endpoint:** `POST http://localhost:3001/trpc/users.listForLogin`

**Açıklama:** PIN login ekranında gösterilecek aktif kullanıcıların listesini döndürür.

#### cURL Örneği:

```bash
curl -X POST http://localhost:3001/trpc/users.listForLogin \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### Filtrelenmiş İstek:

```bash
curl -X POST http://localhost:3001/trpc/users.listForLogin \
  -H "Content-Type: application/json" \
  -d '{
    "companyId": "test-company-1",
    "branchId": "test-branch-1"
  }'
```

#### Beklenen Response:

```json
{
  "result": {
    "data": [
      {
        "id": "test-user-1",
        "firstName": "Elisa",
        "lastName": "Klein",
        "username": "elisa.klein",
        "role": "CASHIER",
        "companyId": "test-company-1",
        "branchId": "test-branch-1",
        "company": {
          "id": "test-company-1",
          "name": "Test Restaurant"
        },
        "branch": {
          "id": "test-branch-1",
          "name": "Ana Şube",
          "code": "MAIN"
        }
      },
      {
        "id": "test-user-2",
        "firstName": "Intan",
        "lastName": "Fauziah",
        "username": "intan.fauziah",
        "role": "WAITER",
        "companyId": "test-company-1",
        "branchId": "test-branch-1",
        "company": {
          "id": "test-company-1",
          "name": "Test Restaurant"
        },
        "branch": {
          "id": "test-branch-1",
          "name": "Ana Şube",
          "code": "MAIN"
        }
      }
    ]
  }
}
```

### 2. PIN ile Giriş

**Endpoint:** `POST http://localhost:3001/trpc/auth.pinLogin`

**Açıklama:** Seçilen kullanıcı ID'si ve PIN ile giriş yapar, session oluşturur.

#### cURL Örneği (Başarılı):

```bash
curl -X POST http://localhost:3001/trpc/auth.pinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-1",
    "pin": "1234"
  }'
```

#### cURL Örneği (Hatalı PIN):

```bash
curl -X POST http://localhost:3001/trpc/auth.pinLogin \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-1",
    "pin": "0000"
  }'
```

#### Başarılı Response:

```json
{
  "result": {
    "data": {
      "user": {
        "id": "test-user-1",
        "firstName": "Elisa",
        "lastName": "Klein",
        "username": "elisa.klein",
        "role": "CASHIER",
        "companyId": "test-company-1",
        "branchId": "test-branch-1",
        "company": {
          "id": "test-company-1",
          "name": "Test Restaurant"
        },
        "branch": {
          "id": "test-branch-1",
          "name": "Ana Şube",
          "code": "MAIN"
        }
      },
      "token": "1703123456789_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6",
      "session": {
        "id": "session-id-123",
        "startedAt": "2024-01-15T10:30:00.000Z",
        "branchId": "test-branch-1"
      }
    }
  }
}
```

#### Hata Response:

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Geçersiz PIN kodu"
  }
}
```

### 3. Çıkış Yapma

**Endpoint:** `POST http://localhost:3001/trpc/auth.logout`

**Açıklama:** Kullanıcının aktif sessionlarını sonlandırır.

#### cURL Örneği:

```bash
curl -X POST http://localhost:3001/trpc/auth.logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
  -d '{}'
```

#### Response:

```json
{
  "result": {
    "data": {
      "success": true
    }
  }
}
```

## PowerShell Test Örnekleri

Windows PowerShell kullanıcıları için:

### Kullanıcı Listesi:

```powershell
$headers = @{
    "Content-Type" = "application/json"
}

$body = @{} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/trpc/users.listForLogin" -Method POST -Headers $headers -Body $body
```

### PIN Login:

```powershell
$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    userId = "test-user-1"
    pin = "1234"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/trpc/auth.pinLogin" -Method POST -Headers $headers -Body $body
```

## Postman Koleksiyonu

Aşağıdaki JSON'u Postman'e import edebilirsiniz:

```json
{
  "info": {
    "name": "PIN Login API Tests",
    "description": "ATROPOS PIN Login API test koleksiyonu",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Get Users for Login",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{}"
        },
        "url": {
          "raw": "http://localhost:3001/trpc/users.listForLogin",
          "protocol": "http",
          "host": ["localhost"],
          "port": "3001",
          "path": ["trpc", "users.listForLogin"]
        }
      }
    },
    {
      "name": "PIN Login - Success",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"userId\": \"test-user-1\",\n  \"pin\": \"1234\"\n}"
        },
        "url": {
          "raw": "http://localhost:3001/trpc/auth.pinLogin",
          "protocol": "http",
          "host": ["localhost"],
          "port": "3001",
          "path": ["trpc", "auth.pinLogin"]
        }
      }
    },
    {
      "name": "PIN Login - Invalid PIN",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"userId\": \"test-user-1\",\n  \"pin\": \"0000\"\n}"
        },
        "url": {
          "raw": "http://localhost:3001/trpc/auth.pinLogin",
          "protocol": "http",
          "host": ["localhost"],
          "port": "3001",
          "path": ["trpc", "auth.pinLogin"]
        }
      }
    },
    {
      "name": "Logout",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          },
          {
            "key": "Authorization",
            "value": "Bearer {{session_token}}"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{}"
        },
        "url": {
          "raw": "http://localhost:3001/trpc/auth.logout",
          "protocol": "http",
          "host": ["localhost"],
          "port": "3001",
          "path": ["trpc", "auth.logout"]
        }
      }
    }
  ],
  "variable": [
    {
      "key": "session_token",
      "value": "",
      "type": "string"
    }
  ]
}
```

## Test Senaryoları

### 1. Pozitif Test Senaryoları

1. **Kullanıcı listesi alma** - Aktif kullanıcıların başarıyla listelenmesi
2. **Geçerli PIN ile giriş** - Doğru kullanıcı ID'si ve PIN ile başarılı giriş
3. **Session oluşturma** - Başarılı giriş sonrası session token'ının döndürülmesi
4. **Çıkış yapma** - Session'ın başarıyla sonlandırılması

### 2. Negatif Test Senaryoları

1. **Geçersiz PIN** - Yanlış PIN ile giriş denemesi
2. **Geçersiz kullanıcı ID'si** - Var olmayan kullanıcı ID'si ile giriş denemesi
3. **PIN'i olmayan kullanıcı** - PIN tanımlanmamış kullanıcı ile giriş denemesi
4. **Pasif kullanıcı** - Aktif olmayan kullanıcı ile giriş denemesi
5. **Geçersiz format** - PIN formatı hatalı (3 haneli, harf içeren vb.)

### 3. Güvenlik Test Senaryoları

1. **SQL Injection** - PIN alanına SQL injection denemesi
2. **XSS** - Kullanıcı adı alanına script injection denemesi
3. **Brute Force** - Aynı kullanıcı için çoklu PIN denemesi
4. **Session Hijacking** - Geçersiz token ile API erişimi denemesi

## Hata Kodları

- `UNAUTHORIZED` - Geçersiz kimlik bilgileri
- `BAD_REQUEST` - Geçersiz input formatı
- `NOT_FOUND` - Kullanıcı bulunamadı
- `FORBIDDEN` - Yetki yetersiz
- `INTERNAL_SERVER_ERROR` - Sunucu hatası

## Notlar

- Tüm PIN'ler 4 haneli rakam olmalıdır
- Session token'ları güvenli şekilde saklanmalıdır
- API rate limiting uygulanabilir
- Loglama ve monitoring önemlidir
