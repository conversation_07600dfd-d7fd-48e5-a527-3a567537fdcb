import { Search, Bell, ChevronDown } from "lucide-react"
import { useAuthStore } from "../../auth/stores/authStore"

/**
 * Dashboard header bileşeni
 * 
 * Bu bileşen:
 * - Şirket/şube bilgilerini gösterir (Aleo Resto, Outlet Sukabumi)
 * - Ara<PERSON> kutusu <PERSON>
 * - Bildirim ikonu gösterir
 * - Kullanıcı bilgilerini (isim, rol) Zustand store'dan çeker
 * - Touch-friendly tasarım (44px minimum)
 */
export function HeaderBar() {
  const { user } = useAuthStore()

  // Kullanıcı bilgilerini güvenli şekilde al
  const userInitials = user 
    ? `${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`.toUpperCase()
    : 'U'
  
  const userName = user 
    ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Kullanıcı'
    : 'Kullanıcı'
  
  const userRole = user?.role === 'CASHIER' ? 'Kasiyer' 
    : user?.role === 'BRANCH_MANAGER' ? 'Şube Müdürü'
    : user?.role === 'ADMIN' ? 'Yönetici'
    : user?.role === 'SUPER_ADMIN' ? 'Süper Admin'
    : user?.role === 'WAITER' ? 'Garson'
    : user?.role === 'KITCHEN' ? 'Mutfak'
    : user?.role === 'REPORTER' ? 'Raporlama'
    : 'Kullanıcı'

  const companyName = user?.company?.name || 'Aleo Resto'
  const branchName = user?.branch?.name || 'Outlet Sukabumi'

  return (
    <header className="flex items-center justify-between px-8 py-3 bg-white border-b border-gray-100 h-[76px]">
      {/* Logo and Outlet Info */}
      <div className="flex items-center gap-3 min-w-0">
        <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
          AR
        </div>
        <div className="hidden sm:block">
          <h1 className="text-sm font-semibold text-gray-900">{companyName}</h1>
          <p className="text-xs text-gray-500">{branchName}</p>
        </div>
      </div>

      {/* Search Bar - Hidden on mobile */}
      <div className="hidden md:flex flex-1 max-w-md mx-6">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search here..."
            className="w-full pl-10 pr-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[44px]"
          />
        </div>
      </div>

      {/* Right Section - Notifications and Profile */}
      <div className="flex items-center gap-2 sm:gap-4">
        {/* Search Icon - Visible on mobile */}
        <button className="md:hidden p-2 min-h-[44px] min-w-[44px] flex items-center justify-center">
          <Search className="w-5 h-5 text-gray-600" />
        </button>

        {/* Notification Bell */}
        <button className="relative p-2 min-h-[44px] min-w-[44px] flex items-center justify-center">
          <Bell className="w-5 h-5 text-gray-600" />
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-medium">2</span>
          </div>
        </button>

        {/* User Profile */}
        <div className="flex items-center gap-2 sm:gap-3 min-h-[44px]">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-purple-600">{userInitials}</span>
          </div>
          <div className="hidden sm:block text-right">
            <p className="text-sm font-medium text-gray-900">{userName}</p>
            <p className="text-xs text-gray-500">{userRole}</p>
          </div>
          <button className="hidden sm:flex items-center justify-center min-h-[44px] min-w-[44px]">
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>
    </header>
  )
}
