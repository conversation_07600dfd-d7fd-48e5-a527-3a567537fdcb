import { httpBatchLink } from '@trpc/client'
import { createTRPCReact } from '@trpc/react-query'
import { QueryClient } from '@tanstack/react-query'
import type { AppRouter } from '../server/routers'

// tRPC React hooks
export const trpc = createTRPCReact<AppRouter>()

// Query client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: (failureCount, error: any) => {
        // 401 ve 403 hatalarında retry yapma
        if (error?.data?.code === 'UNAUTHORIZED' || error?.data?.code === 'FORBIDDEN') {
          return false
        }
        return failureCount < 3
      },
    },
    mutations: {
      retry: false,
    },
  },
})

// tRPC client
export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: 'http://localhost:3001/trpc', // Backend server URL
      headers: () => {
        // Auth token'ı localStorage'dan al
        const token = localStorage.getItem('auth-token')
        return token ? { authorization: `Bear<PERSON> ${token}` } : {}
      },
    }),
  ],
})



