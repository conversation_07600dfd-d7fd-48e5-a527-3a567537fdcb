import React from 'react'
import { trpc } from '../../../lib/trpc-client'
import { toast } from 'sonner'

/**
 * Vergi yönetimi için TanStack Query hooks
 * 
 * Bu hooks:
 * - Vergi listesi getirme
 * - Vergi detayı getirme
 * - Vergi oluşturma, g<PERSON><PERSON><PERSON><PERSON>, silme
 * - Error handling ve toast bildirimleri
 * - C<PERSON> invalidation
 */

/**
 * Vergi listesi getiren hook
 */
export function useTaxes() {
  return trpc.products.tax.list.useQuery(
    undefined,
    {
      staleTime: 10 * 60 * 1000, // 10 dakika (vergiler sık değişmez)
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error) => {
        console.error('Vergi listesi getirme hatası:', error)
        toast.error('Vergi listesi yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Tek vergi detayı getiren hook
 */
export function useTax(taxId: string) {
  return trpc.products.tax.getById.useQuery(
    { id: taxId },
    {
      enabled: !!taxId,
      staleTime: 10 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Vergi detayı getirme hatası:', error)
        toast.error('Vergi detayları yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Vergi oluşturma hook'u
 */
export function useCreateTax() {
  const utils = trpc.useUtils()

  return trpc.products.tax.create.useMutation({
    onSuccess: (data) => {
      toast.success('Vergi başarıyla oluşturuldu')
      
      // Cache'leri invalidate et
      utils.products.tax.list.invalidate()
    },
    onError: (error) => {
      console.error('Vergi oluşturma hatası:', error)
      
      const errorMessage = error.message || 'Vergi oluşturulurken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Vergi güncelleme hook'u
 */
export function useUpdateTax() {
  const utils = trpc.useUtils()

  return trpc.products.tax.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Vergi başarıyla güncellendi')
      
      // İlgili cache'leri invalidate et
      utils.products.tax.list.invalidate()
      utils.products.tax.getById.invalidate({ id: variables.id })
    },
    onError: (error) => {
      console.error('Vergi güncelleme hatası:', error)
      
      const errorMessage = error.message || 'Vergi güncellenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Vergi silme hook'u
 */
export function useDeleteTax() {
  const utils = trpc.useUtils()

  return trpc.products.tax.delete.useMutation({
    onSuccess: () => {
      toast.success('Vergi başarıyla silindi')
      
      // Cache'leri invalidate et
      utils.products.tax.list.invalidate()
    },
    onError: (error) => {
      console.error('Vergi silme hatası:', error)
      
      const errorMessage = error.message || 'Vergi silinirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Aktif vergileri getiren hook (dropdown'lar için)
 */
export function useActiveTaxes() {
  return trpc.products.tax.list.useQuery(
    undefined,
    {
      staleTime: 10 * 60 * 1000,
      retry: 3,
      select: (data) => data.taxes.filter(tax => tax.active),
      onError: (error) => {
        console.error('Aktif vergi listesi getirme hatası:', error)
        toast.error('Vergi listesi yüklenirken bir hata oluştu')
      }
    }
  )
}
