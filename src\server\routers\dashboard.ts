import { TRPCError } from '@trpc/server'
import { router, protectedProcedure, requireRole } from '../trpc'
import { DashboardService } from '../services/dashboardService'
import {
  overviewStatsInputSchema,
  overviewStatsOutputSchema,
  popularDishesInputSchema,
  popularDishesOutputSchema,
  outOfStockInputSchema,
  outOfStockOutputSchema,
  ordersListInputSchema,
  ordersListOutputSchema,
} from '../../lib/validations/dashboard'

/**
 * Dashboard Router - Dashboard sayfası için tRPC prosedürleri
 * 
 * Bu router aşağıdaki prosedürleri içerir:
 * - getOverviewStats: <PERSON><PERSON>t istatistikler (kazanç, sipariş sayıları)
 * - getPopularDishes: En çok satan ürünler
 * - getOutOfStockItems: Stok kritik ürünler
 * - getOrdersList: <PERSON><PERSON><PERSON><PERSON> list<PERSON> (duruma göre filtrelenmiş)
 * 
 * Yetkilendirme: BRANCH_MANAGER, REPORTER ve üzeri roller erişebilir
 */

// Dashboard erişimi için yetki kontrolü
const dashboardProcedure = protectedProcedure.use(
  requireRole(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER', 'REPORTER', 'CASHIER'])
)

export const dashboardRouter = router({
  /**
   * Dashboard özet istatistiklerini getirir
   * 
   * Bu prosedür:
   * - Günlük toplam kazancı hesaplar (ödenen siparişler)
   * - Devam eden sipariş sayısını getirir (PREPARING, PENDING)
   * - Bekleme listesi sayısını getirir (rezerve/dolu masalar)
   * - Önceki güne göre yüzde değişimleri hesaplar
   * - Şube filtrelemesi yapar (admin'ler için opsiyonel)
   */
  getOverviewStats: dashboardProcedure
    .input(overviewStatsInputSchema)
    .output(overviewStatsOutputSchema)
    .query(async ({ input, ctx }) => {
      try {
        // Yetki kontrolü - sadece kendi şubesine veya admin yetkisi varsa tüm şubelere erişim
        const userBranchId = ctx.user.branchId
        const isAdmin = ['SUPER_ADMIN', 'ADMIN'].includes(ctx.user.role)
        
        if (input.branchId && !isAdmin && input.branchId !== userBranchId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Bu şubenin verilerine erişim yetkiniz yok',
          })
        }

        const stats = await DashboardService.getOverviewStats(
          input,
          ctx.user.companyId,
          userBranchId
        )

        return stats
      } catch (error) {
        console.error('Dashboard getOverviewStats error:', error)
        
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Özet istatistikler alınırken bir hata oluştu',
        })
      }
    }),

  /**
   * En çok satan ürünleri getirir
   * 
   * Bu prosedür:
   * - Belirtilen tarih aralığında en çok satan ürünleri listeler
   * - Ürün adı, satış miktarı ve görsel bilgilerini döndürür
   * - Şube filtrelemesi yapar
   * - Limit parametresi ile sonuç sayısını sınırlar
   */
  getPopularDishes: dashboardProcedure
    .input(popularDishesInputSchema)
    .output(popularDishesOutputSchema)
    .query(async ({ input, ctx }) => {
      try {
        // Yetki kontrolü
        const userBranchId = ctx.user.branchId
        const isAdmin = ['SUPER_ADMIN', 'ADMIN'].includes(ctx.user.role)
        
        if (input.branchId && !isAdmin && input.branchId !== userBranchId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Bu şubenin verilerine erişim yetkiniz yok',
          })
        }

        const popularDishes = await DashboardService.getPopularDishes(
          input,
          ctx.user.companyId,
          userBranchId
        )

        return popularDishes
      } catch (error) {
        console.error('Dashboard getPopularDishes error:', error)
        
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Popüler yemekler alınırken bir hata oluştu',
        })
      }
    }),

  /**
   * Stok kritik ürünleri getirir
   * 
   * Bu prosedür:
   * - Stok takibi yapılan ürünleri kontrol eder
   * - Kritik seviyenin altında olan ürünleri listeler
   * - Mevcut stok, kritik seviye ve birim bilgilerini döndürür
   * - Şirket genelinde arama yapar (şube bağımsız)
   */
  getOutOfStockItems: dashboardProcedure
    .input(outOfStockInputSchema)
    .output(outOfStockOutputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const outOfStockItems = await DashboardService.getOutOfStockItems(
          input,
          ctx.user.companyId
        )

        return outOfStockItems
      } catch (error) {
        console.error('Dashboard getOutOfStockItems error:', error)
        
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Stok kritik ürünler alınırken bir hata oluştu',
        })
      }
    }),

  /**
   * Sipariş listesini getirir
   * 
   * Bu prosedür:
   * - Durum filtresine göre siparişleri listeler
   * - 'inProgress': Hazırlanan/bekleyen siparişler
   * - 'waitingForPayment': Ödeme bekleyen siparişler
   * - Arama özelliği (sipariş no, müşteri adı, masa no)
   * - Masa numarası, müşteri adı, kalem sayısı bilgilerini döndürür
   */
  getOrdersList: dashboardProcedure
    .input(ordersListInputSchema)
    .output(ordersListOutputSchema)
    .query(async ({ input, ctx }) => {
      try {
        // Yetki kontrolü
        const userBranchId = ctx.user.branchId
        const isAdmin = ['SUPER_ADMIN', 'ADMIN'].includes(ctx.user.role)
        
        if (input.branchId && !isAdmin && input.branchId !== userBranchId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Bu şubenin verilerine erişim yetkiniz yok',
          })
        }

        const ordersList = await DashboardService.getOrdersList(
          input,
          ctx.user.companyId,
          userBranchId
        )

        return ordersList
      } catch (error) {
        console.error('Dashboard getOrdersList error:', error)
        
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Sipariş listesi alınırken bir hata oluştu',
        })
      }
    }),
})
