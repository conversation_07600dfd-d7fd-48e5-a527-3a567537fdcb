import { useState, useMemo } from 'react'
import { trpc } from '../../../lib/trpc-client'
import { useDebounce } from '../../../hooks/useDebounce'
import type { OrdersListInput } from '../types'

/**
 * Sipariş listesini getiren hook
 * 
 * Bu hook:
 * - Sipariş listesini duruma göre filtreler (In Progress, Waiting for Payment)
 * - Arama kutusu için debounce özelliği sağlar
 * - Sipariş durumlarını görsel olarak yansıtır
 * - Real-time güncellemeler için otomatik refetch yapar
 */
export function useOrdersList(initialInput?: Partial<OrdersListInput>) {
  const [searchQuery, setSearchQuery] = useState('')
  const [status, setStatus] = useState<'inProgress' | 'waitingForPayment'>('inProgress')
  
  // 300ms debounce for search (kural.md'deki "Search = 300ms debounce")
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  
  const input: OrdersListInput = useMemo(() => ({
    status,
    search: debouncedSearchQuery || undefined,
    limit: 20,
    ...initialInput,
  }), [status, debouncedSearchQuery, initialInput])
  
  const query = trpc.dashboard.getOrdersList.useQuery(input, {
    staleTime: 30 * 1000, // 30 saniye
    refetchInterval: 15 * 1000, // 15 saniyede bir otomatik güncelle
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
  
  return {
    ...query,
    searchQuery,
    setSearchQuery,
    status,
    setStatus,
    // Filtered orders based on search
    filteredOrders: query.data || [],
  }
}
