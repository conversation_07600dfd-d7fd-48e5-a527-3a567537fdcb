import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { AuthStore, UserWithRelations, LoginForm } from '../../../types'
import { storage } from '../../../lib/utils'

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      login: async (credentials: LoginForm) => {
        try {
          // Bu kısım tRPC client ile yapılacak
          // Şimdilik mock implementation
          const mockUser: UserWithRelations = {
            id: '1',
            companyId: '1',
            branchId: '1',
            username: credentials.username,
            password: '', // Güvenlik için boş
            pin: null,
            firstName: 'Admin',
            lastName: 'User',
            email: '<EMAIL>',
            phone: null,
            role: 'SUPER_ADMIN' as any,
            permissions: null,
            active: true,
            lastLoginAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
            company: {
              id: '1',
              name: 'Demo Restaurant',
              taxNumber: '1234567890',
              taxOffice: 'Kadıköy',
              address: 'Demo Adres',
              phone: '+90 212 123 45 67',
              email: '<EMAIL>',
              logo: null,
              eArchiveUsername: null,
              eArchivePassword: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
            branch: {
              id: '1',
              companyId: '1',
              code: 'MAIN',
              name: 'Ana Şube',
              address: 'Ana Şube Adresi',
              phone: '+90 212 123 45 67',
              email: null,
              serverIp: null,
              serverPort: null,
              isMainBranch: true,
              openingTime: '09:00',
              closingTime: '23:00',
              cashRegisterId: null,
              active: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          }

          const mockToken = 'mock-jwt-token'

          set({
            user: mockUser,
            token: mockToken,
            isAuthenticated: true,
          })

          // Token'ı localStorage'a kaydet
          storage.set('auth-token', mockToken)
        } catch (error) {
          console.error('Login error:', error)
          throw error
        }
      },

      pinLogin: async (pin: string) => {
        try {
          // PIN ile giriş mock implementation
          if (pin === '1234') {
            await get().login({ username: 'admin', password: 'admin123' })
          } else {
            throw new Error('Geçersiz PIN kodu')
          }
        } catch (error) {
          console.error('PIN login error:', error)
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })

        // Token'ı localStorage'dan sil
        storage.remove('auth-token')
      },

      setUser: (user: UserWithRelations) => {
        set({
          user,
          isAuthenticated: true,
          token: localStorage.getItem('auth-token') || null
        })
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

