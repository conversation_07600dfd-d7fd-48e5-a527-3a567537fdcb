import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON><PERSON> without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  store: {
    get: (key: string) => ipcRenderer.invoke('store-get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('store-set', key, value),
    delete: (key: string) => ipcRenderer.invoke('store-delete', key),
  },
  app: {
    getVersion: () => ipcRenderer.invoke('app-version'),
    quit: () => ipcRenderer.invoke('app-quit'),
  },
})

// Type definitions for the exposed API
export interface ElectronAPI {
  store: {
    get: (key: string) => Promise<any>
    set: (key: string, value: any) => Promise<void>
    delete: (key: string) => Promise<void>
  }
  app: {
    getVersion: () => Promise<string>
    quit: () => Promise<void>
  }
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

