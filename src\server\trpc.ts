import { initTRPC, TRPCError } from '@trpc/server'
import { z } from 'zod'
import { prisma } from './db'

// Context type
export interface Context {
  prisma: typeof prisma
  user?: {
    id: string
    companyId: string
    branchId?: string
    role: string
  }
}

// Initialize tRPC
const t = initTRPC.context<Context>().create()

// Base router and procedure helpers
export const router = t.router
export const publicProcedure = t.procedure

// Auth middleware
const isAuthenticated = t.middleware(({ next, ctx }) => {
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '<PERSON><PERSON><PERSON> yapmanız gerekiyor',
    })
  }
  return next({
    ctx: {
      ...ctx,
      user: ctx.user,
    },
  })
})

// Protected procedure
export const protectedProcedure = t.procedure.use(isAuthenticated)

// Role-based middleware
export const requireRole = (roles: string[]) =>
  t.middleware(({ next, ctx }) => {
    if (!ctx.user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: '<PERSON><PERSON><PERSON> yapmanız gerekiyor',
      })
    }

    if (!roles.includes(ctx.user.role)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'Bu işlem için yetkiniz bulunmuyor',
      })
    }

    return next({ ctx })
  })

// Admin procedure
export const adminProcedure = t.procedure.use(
  requireRole(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])
)

// Common input schemas
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const idSchema = z.object({
  id: z.string().cuid(),
})

export const companyFilterSchema = z.object({
  companyId: z.string().cuid().optional(),
})

export const branchFilterSchema = z.object({
  branchId: z.string().cuid().optional(),
})

