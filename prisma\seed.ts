import { PrismaClient, UserRole, PaymentMethodType, ProductUnit } from '@prisma/client'
import bcrypt from 'bcryptjs'
import { Decimal } from 'decimal.js'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Veritabanı seed işlemi başlıyor...')

  // 1. Şirket oluştur
  const company = await prisma.company.create({
    data: {
      name: 'Demo Restaurant',
      taxNumber: '1234567890',
      taxOffice: 'Kadıköy',
      address: 'Demo Adres, İstanbul',
      phone: '+90 212 123 45 67',
      email: '<EMAIL>',
    },
  })

  console.log('✅ Şirket oluşturuldu:', company.name)

  // 2. Şube oluştur
  const branch = await prisma.branch.create({
    data: {
      companyId: company.id,
      code: 'MAIN',
      name: 'Ana Şube',
      address: 'Ana Şube Adresi, İstanbul',
      phone: '+90 212 123 45 67',
      isMainBranch: true,
      openingTime: '09:00',
      closingTime: '23:00',
    },
  })

  console.log('✅ Şube oluşturuldu:', branch.name)

  // 3. KDV oranları oluştur
  const taxes = await Promise.all([
    prisma.tax.create({
      data: {
        companyId: company.id,
        name: 'KDV %8',
        rate: new Decimal(8),
        code: 'VAT8',
        isDefault: false,
      },
    }),
    prisma.tax.create({
      data: {
        companyId: company.id,
        name: 'KDV %18',
        rate: new Decimal(18),
        code: 'VAT18',
        isDefault: true,
      },
    }),
  ])

  console.log('✅ KDV oranları oluşturuldu')

  // 4. Ödeme yöntemleri oluştur
  const paymentMethods = await Promise.all([
    prisma.paymentMethod.create({
      data: {
        companyId: company.id,
        name: 'Nakit',
        code: 'CASH',
        type: PaymentMethodType.CASH,
        displayOrder: 1,
      },
    }),
    prisma.paymentMethod.create({
      data: {
        companyId: company.id,
        name: 'Kredi Kartı',
        code: 'CC',
        type: PaymentMethodType.CREDIT_CARD,
        commissionRate: new Decimal(2.5),
        displayOrder: 2,
      },
    }),
    prisma.paymentMethod.create({
      data: {
        companyId: company.id,
        name: 'Yemek Kartı',
        code: 'MEAL',
        type: PaymentMethodType.MEAL_CARD,
        displayOrder: 3,
      },
    }),
  ])

  console.log('✅ Ödeme yöntemleri oluşturuldu')

  // 5. Admin kullanıcı oluştur
  const hashedPassword = await bcrypt.hash('admin123', 10)
  const hashedPin = await bcrypt.hash('1234', 10)

  const adminUser = await prisma.user.create({
    data: {
      companyId: company.id,
      branchId: branch.id,
      username: 'admin',
      password: hashedPassword,
      pin: hashedPin,
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      role: UserRole.SUPER_ADMIN,
    },
  })

  console.log('✅ Admin kullanıcı oluşturuldu:', adminUser.username)

  // 6. Demo kullanıcılar oluştur
  const demoUsers = await Promise.all([
    prisma.user.create({
      data: {
        companyId: company.id,
        branchId: branch.id,
        username: 'kasiyer1',
        password: await bcrypt.hash('kasiyer123', 10),
        pin: await bcrypt.hash('1111', 10),
        firstName: 'Ahmet',
        lastName: 'Yılmaz',
        role: UserRole.CASHIER,
      },
    }),
    prisma.user.create({
      data: {
        companyId: company.id,
        branchId: branch.id,
        username: 'garson1',
        password: await bcrypt.hash('garson123', 10),
        pin: await bcrypt.hash('2222', 10),
        firstName: 'Ayşe',
        lastName: 'Demir',
        role: UserRole.WAITER,
      },
    }),
  ])

  console.log('✅ Demo kullanıcılar oluşturuldu')

  // 7. Kategoriler oluştur
  const categories = await Promise.all([
    prisma.category.create({
      data: {
        companyId: company.id,
        name: 'Sıcak İçecekler',
        description: 'Kahve, çay ve sıcak içecekler',
        color: '#FF6B6B',
        icon: 'coffee',
        displayOrder: 1,
      },
    }),
    prisma.category.create({
      data: {
        companyId: company.id,
        name: 'Soğuk İçecekler',
        description: 'Meşrubat, meyve suyu ve soğuk içecekler',
        color: '#4ECDC4',
        icon: 'glass-water',
        displayOrder: 2,
      },
    }),
    prisma.category.create({
      data: {
        companyId: company.id,
        name: 'Ana Yemekler',
        description: 'Et, tavuk ve balık yemekleri',
        color: '#45B7D1',
        icon: 'utensils',
        displayOrder: 3,
      },
    }),
    prisma.category.create({
      data: {
        companyId: company.id,
        name: 'Tatlılar',
        description: 'Pasta, dondurma ve tatlılar',
        color: '#F7DC6F',
        icon: 'cake',
        displayOrder: 4,
      },
    }),
  ])

  console.log('✅ Kategoriler oluşturuldu')

  // 8. Ürünler oluştur
  const products = await Promise.all([
    // Sıcak İçecekler
    prisma.product.create({
      data: {
        companyId: company.id,
        categoryId: categories[0].id,
        code: 'COFFEE001',
        name: 'Türk Kahvesi',
        description: 'Geleneksel Türk kahvesi',
        basePrice: new Decimal(15.00),
        taxId: taxes[0].id,
        unit: ProductUnit.PIECE,
        displayOrder: 1,
      },
    }),
    prisma.product.create({
      data: {
        companyId: company.id,
        categoryId: categories[0].id,
        code: 'TEA001',
        name: 'Çay',
        description: 'Demli çay',
        basePrice: new Decimal(8.00),
        taxId: taxes[0].id,
        unit: ProductUnit.PIECE,
        displayOrder: 2,
      },
    }),
    // Soğuk İçecekler
    prisma.product.create({
      data: {
        companyId: company.id,
        categoryId: categories[1].id,
        code: 'COLA001',
        name: 'Kola',
        description: 'Soğuk kola',
        basePrice: new Decimal(12.00),
        taxId: taxes[1].id,
        unit: ProductUnit.PIECE,
        displayOrder: 1,
      },
    }),
    // Ana Yemekler
    prisma.product.create({
      data: {
        companyId: company.id,
        categoryId: categories[2].id,
        code: 'KEBAB001',
        name: 'Adana Kebap',
        description: 'Acılı kıyma kebabı',
        basePrice: new Decimal(85.00),
        taxId: taxes[0].id,
        unit: ProductUnit.PORTION,
        preparationTime: 15,
        displayOrder: 1,
      },
    }),
    // Tatlılar
    prisma.product.create({
      data: {
        companyId: company.id,
        categoryId: categories[3].id,
        code: 'BAKLAVA001',
        name: 'Baklava',
        description: 'Antep fıstıklı baklava',
        basePrice: new Decimal(25.00),
        taxId: taxes[0].id,
        unit: ProductUnit.PIECE,
        displayOrder: 1,
      },
    }),
  ])

  console.log('✅ Ürünler oluşturuldu')

  // 9. Masa alanları ve masalar oluştur
  const tableArea = await prisma.tableArea.create({
    data: {
      branchId: branch.id,
      name: 'Ana Salon',
      displayOrder: 1,
    },
  })

  const tables = await Promise.all([
    prisma.table.create({
      data: {
        branchId: branch.id,
        areaId: tableArea.id,
        number: '1',
        capacity: 4,
        positionX: 100,
        positionY: 100,
      },
    }),
    prisma.table.create({
      data: {
        branchId: branch.id,
        areaId: tableArea.id,
        number: '2',
        capacity: 2,
        positionX: 250,
        positionY: 100,
      },
    }),
    prisma.table.create({
      data: {
        branchId: branch.id,
        areaId: tableArea.id,
        number: '3',
        capacity: 6,
        positionX: 100,
        positionY: 250,
      },
    }),
  ])

  console.log('✅ Masalar oluşturuldu')

  // 10. Demo müşteri oluştur
  const customer = await prisma.customer.create({
    data: {
      firstName: 'Mehmet',
      lastName: 'Özkan',
      phone: '+90 532 123 45 67',
      email: '<EMAIL>',
      address: 'Demo Müşteri Adresi, İstanbul',
    },
  })

  console.log('✅ Demo müşteri oluşturuldu')

  console.log('🎉 Seed işlemi tamamlandı!')
  console.log('\n📋 Giriş Bilgileri:')
  console.log('👤 Kullanıcı Adı: admin')
  console.log('🔑 Şifre: admin123')
  console.log('📱 PIN: 1234')
  console.log('\n🏪 Demo Veriler:')
  console.log(`- Şirket: ${company.name}`)
  console.log(`- Şube: ${branch.name}`)
  console.log(`- Kategoriler: ${categories.length} adet`)
  console.log(`- Ürünler: ${products.length} adet`)
  console.log(`- Masalar: ${tables.length} adet`)
  console.log(`- Kullanıcılar: ${demoUsers.length + 1} adet`)
}

main()
  .catch((e) => {
    console.error('❌ Seed işlemi sırasında hata:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

