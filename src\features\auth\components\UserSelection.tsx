import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import type { UserForLogin } from '../../../lib/validations/auth'
import { CheckIcon } from '../../../assets/icons/check-icon'

interface UserSelectionProps {
  users: UserForLogin[]
  selectedUser: UserForLogin | null
  onUserSelect: (user: UserForLogin) => void
  isLoading?: boolean
}

export function UserSelection({ 
  users, 
  selectedUser, 
  onUserSelect, 
  isLoading = false 
}: UserSelectionProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleUserSelect = (user: UserForLogin) => {
    onUserSelect(user)
    setIsOpen(false)
  }

  const toggleDropdown = () => {
    if (!isLoading && users.length > 0) {
      setIsOpen(!isOpen)
    }
  }

  if (isLoading) {
    return (
      <div className="w-full">
        <p className="font-inter font-normal text-[14px] leading-[21px] text-[#636566] text-center mb-2">
          Hesabınızı seçerek vardiyaya başlayın.
        </p>
        <div className="bg-[#f5f5f5] rounded-xl p-5 animate-pulse">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-[#d9d9d9] rounded-full" />
            <div className="flex-1">
              <div className="h-4 bg-[#d9d9d9] rounded mb-1" />
              <div className="h-3 bg-[#d9d9d9] rounded w-2/3" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full">
      <p className="font-inter font-normal text-[14px] leading-[21px] text-[#636566] text-center mb-2">
        Hesabınızı seçerek vardiyaya başlayın.
      </p>

      <div className="relative">
        {/* Seçilen kullanıcı her zaman üstte gösteriliyor (senin kodun gibi) */}
        <button
          onClick={toggleDropdown}
          className={`w-full flex items-center gap-3 p-[10px] px-5 rounded-xl bg-[#f5f5f5] border-2 border-transparent transition-colors ${
            isOpen ? 'border-[#025cca] bg-white' : 'hover:bg-[#eeeeee]'
          }`}
        >
          {selectedUser ? (
            <>
              <div className="w-10 h-10 bg-[#d9d9d9] rounded-full flex items-center justify-center">
                <span className="text-[#636566] font-medium text-sm">
                  {selectedUser.firstName.charAt(0)}{selectedUser.lastName.charAt(0)}
                </span>
              </div>
              <div className="flex-1 text-left">
                <p className="font-inter font-medium text-[16px] leading-[24px] text-[#202325]">
                  {selectedUser.firstName} {selectedUser.lastName}
                </p>
                <p className="font-inter font-normal text-[14px] leading-[21px] text-[#636566]">
                  {selectedUser.role === 'ADMIN' ? '04:00 PM - 12:00 PM' : '08:00 AM - 04:00 PM'}
                </p>
              </div>
              {isOpen ? (
                <ChevronUp className="w-6 h-6 text-[#202325]" />
              ) : (
                <ChevronDown className="w-6 h-6 text-[#202325]" />
              )}
            </>
          ) : (
            <>
              <div className="flex-1 text-left">
                <span className="font-inter font-normal text-[14px] leading-[21px] text-[#636566]">
                  Kullanıcı seçin...
                </span>
              </div>
              <ChevronDown className="w-5 h-5 text-[#636566]" />
            </>
          )}
        </button>

        {/* Dropdown - sadece seçilen kullanıcı dışındaki kullanıcılar (senin kodun gibi) */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-neutral-100 rounded-xl shadow-[0px_4px_16px_0px_rgba(0,0,0,0.08)] z-10 p-3">
            {users
              .filter((user) => user.id !== selectedUser?.id)
              .map((user) => (
                <button
                  key={user.id}
                  onClick={() => handleUserSelect(user)}
                  className="w-full flex items-center gap-3 p-[10px] rounded-xl transition-colors hover:bg-[#f0f8ff]"
                >
                  <div className="w-10 h-10 bg-[#d9d9d9] rounded-full flex items-center justify-center">
                    <span className="text-[#636566] font-medium text-sm">
                      {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1 text-left">
                    <p className="font-inter font-medium text-[16px] leading-[24px] text-[#202325]">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="font-inter font-normal text-[14px] leading-[21px] text-[#636566]">
                      {user.role === 'ADMIN' ? '04:00 PM - 12:00 PM' : '08:00 AM - 04:00 PM'}
                    </p>
                  </div>
                </button>
              ))}
          </div>
        )}
      </div>
    </div>
  )
}
