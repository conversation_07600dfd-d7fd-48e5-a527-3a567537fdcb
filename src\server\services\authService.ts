import bcrypt from 'bcryptjs'
import crypto from 'crypto'
import { TRPCError } from '@trpc/server'
import { prisma } from '../db'
import type { User, Session } from '@prisma/client'

/**
 * AuthService - Kimlik doğrulama ve oturum yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - PIN hashleme ve doğrulama
 * - G<PERSON>venli token oluşturma
 * - Session yönetimi
 * - Kullanıcı doğrulama
 */
export class AuthService {
  /**
   * PIN'i güvenli şekilde hashler
   * @param pin - 4 haneli PIN kodu
   * @returns Hashlenmiş PIN
   */
  static async hashPin(pin: string): Promise<string> {
    if (!pin || pin.length !== 4 || !/^\d{4}$/.test(pin)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'PIN 4 haneli sayı olmalıdır',
      })
    }

    return bcrypt.hash(pin, 10)
  }

  /**
   * PIN'i doğrular
   * @param pin - Girilen PIN
   * @param hashedPin - Veritabanındaki hashlenmiş PIN
   * @returns PIN doğru mu?
   */
  static async verifyPin(pin: string, hashedPin: string): Promise<boolean> {
    if (!pin || !hashedPin) {
      return false
    }

    return bcrypt.compare(pin, hashedPin)
  }

  /**
   * Şifreyi güvenli şekilde hashler
   * @param password - Şifre
   * @returns Hashlenmiş şifre
   */
  static async hashPassword(password: string): Promise<string> {
    if (!password || password.length < 6) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Şifre en az 6 karakter olmalıdır',
      })
    }

    return bcrypt.hash(password, 10)
  }

  /**
   * Şifreyi doğrular
   * @param password - Girilen şifre
   * @param hashedPassword - Veritabanındaki hashlenmiş şifre
   * @returns Şifre doğru mu?
   */
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    if (!password || !hashedPassword) {
      return false
    }

    return bcrypt.compare(password, hashedPassword)
  }

  /**
   * Güvenli, rastgele token oluşturur
   * @returns Güvenli token string
   */
  static generateSecureToken(): string {
    // 32 byte rastgele veri oluştur ve hex'e çevir
    const randomBytes = crypto.randomBytes(32)
    const timestamp = Date.now().toString(36)
    const randomString = randomBytes.toString('hex')
    
    // Timestamp + random string kombinasyonu
    return `${timestamp}_${randomString}`
  }

  /**
   * Kullanıcı için yeni session oluşturur
   * @param userId - Kullanıcı ID'si
   * @param branchId - Şube ID'si
   * @param deviceInfo - Cihaz bilgisi (opsiyonel)
   * @returns Oluşturulan session
   */
  static async createSession(
    userId: string,
    branchId: string,
    deviceInfo?: string
  ): Promise<Session> {
    // Mevcut aktif sessionları sonlandır (tek oturum politikası)
    await prisma.session.updateMany({
      where: {
        userId,
        endedAt: null,
      },
      data: {
        endedAt: new Date(),
      },
    })

    // Yeni token oluştur
    const token = this.generateSecureToken()

    // Yeni session oluştur
    const session = await prisma.session.create({
      data: {
        userId,
        branchId,
        token,
        deviceInfo,
        startedAt: new Date(),
        lastActivityAt: new Date(),
      },
    })

    return session
  }

  /**
   * Session'ı token ile bulur ve doğrular
   * @param token - Session token'ı
   * @returns Session ve kullanıcı bilgileri
   */
  static async validateSession(token: string) {
    if (!token) {
      return null
    }

    const session = await prisma.session.findUnique({
      where: { token },
      include: {
        user: {
          include: {
            company: true,
            branch: true,
          },
        },
      },
    })

    if (!session || session.endedAt) {
      return null
    }

    // Session'ın son aktivite zamanını güncelle
    await prisma.session.update({
      where: { id: session.id },
      data: { lastActivityAt: new Date() },
    })

    return session
  }

  /**
   * Session'ı sonlandırır
   * @param token - Session token'ı
   */
  static async endSession(token: string): Promise<void> {
    await prisma.session.updateMany({
      where: {
        token,
        endedAt: null,
      },
      data: {
        endedAt: new Date(),
      },
    })
  }

  /**
   * Kullanıcının aktif kullanıcılar listesi için güvenli bilgilerini döndürür
   * @param user - Kullanıcı objesi
   * @returns Güvenli kullanıcı bilgileri
   */
  static getSafeUserInfo(user: User & { company?: any; branch?: any }) {
    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      role: user.role,
      companyId: user.companyId,
      branchId: user.branchId,
      company: user.company ? {
        id: user.company.id,
        name: user.company.name,
      } : undefined,
      branch: user.branch ? {
        id: user.branch.id,
        name: user.branch.name,
        code: user.branch.code,
      } : undefined,
    }
  }

  /**
   * Kullanıcının son giriş zamanını günceller
   * @param userId - Kullanıcı ID'si
   */
  static async updateLastLogin(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() },
    })
  }

  /**
   * Aktif kullanıcıları getirir (PIN login için)
   * @param companyId - Şirket ID'si (opsiyonel filtreleme için)
   * @param branchId - Şube ID'si (opsiyonel filtreleme için)
   * @returns Aktif kullanıcılar listesi
   */
  static async getActiveUsersForLogin(companyId?: string, branchId?: string) {
    const where: any = {
      active: true,
      pin: { not: null }, // PIN'i olan kullanıcılar
    }

    if (companyId) {
      where.companyId = companyId
    }

    if (branchId) {
      where.OR = [
        { branchId }, // Belirli şubeye ait
        { branchId: null }, // Tüm şubelere erişimi olan
      ]
    }

    const users = await prisma.user.findMany({
      where,
      include: {
        company: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' },
      ],
    })

    // Güvenli bilgileri döndür
    return users.map(user => this.getSafeUserInfo(user))
  }

  /**
   * Kullanıcıyı ID ile bulur ve doğrular
   * @param userId - Kullanıcı ID'si
   * @returns Kullanıcı bilgileri
   */
  static async findUserById(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        company: true,
        branch: true,
      },
    })

    if (!user || !user.active) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kullanıcı bulunamadı veya aktif değil',
      })
    }

    return user
  }
}
