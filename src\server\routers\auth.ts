import { TRPCError } from '@trpc/server'
import { router, publicProcedure, protectedProcedure } from '../trpc'
import { AuthService } from '../services/authService'
import {
  loginInputSchema,
  pinLoginInputSchema,
  changePasswordInputSchema,
  changePinInputSchema,
  pinLoginResponseSchema,
  successResponseSchema,
} from '../../lib/validations/auth'

export const authRouter = router({
  // Kullanıcı adı ve şifre ile giriş
  login: publicProcedure
    .input(loginInputSchema)
    .mutation(async ({ input, ctx }) => {
      const { username, password } = input

      const user = await ctx.prisma.user.findUnique({
        where: { username },
        include: {
          company: true,
          branch: true,
        },
      })

      if (!user || !user.active) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '<PERSON><PERSON><PERSON><PERSON><PERSON> kullanıcı adı veya şifre',
        })
      }

      const isValidPassword = await AuthService.verifyPassword(password, user.password)
      if (!isValidPassword) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Geçersiz kullanıcı adı veya şifre',
        })
      }

      // Son giriş zamanını güncelle
      await AuthService.updateLastLogin(user.id)

      return {
        user: AuthService.getSafeUserInfo(user),
      }
    }),

  // PIN ile hızlı giriş
  pinLogin: publicProcedure
    .input(pinLoginInputSchema)
    .output(pinLoginResponseSchema)
    .mutation(async ({ input }) => {
      const { userId, pin } = input

      try {
        // 1. Kullanıcıyı getir ve doğrula
        const user = await AuthService.findUserById(userId)

        // 2. PIN kontrolü
        if (!user.pin) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Bu kullanıcı için PIN tanımlanmamış',
          })
        }

        // 3. PIN doğrulaması
        const isPinValid = await AuthService.verifyPin(pin, user.pin)
        if (!isPinValid) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Geçersiz PIN kodu',
          })
        }

        // 4. Kullanıcının branchId'sini belirle
        const branchId = user.branchId || user.company.id // Eğer branchId yoksa company'yi kullan

        // 5. Yeni session oluştur
        const session = await AuthService.createSession(
          user.id,
          branchId,
          'PIN Login' // Device info
        )

        // 6. Son giriş zamanını güncelle
        await AuthService.updateLastLogin(user.id)

        // 7. Güvenli kullanıcı bilgilerini döndür
        return {
          user: AuthService.getSafeUserInfo(user),
          token: session.token,
          session: {
            id: session.id,
            startedAt: session.startedAt,
            branchId: session.branchId,
          },
        }
      } catch (error) {
        // Hata loglaması
        console.error('PIN Login Error:', {
          userId,
          error: error instanceof Error ? error.message : 'Unknown error',
        })

        // TRPCError'ları olduğu gibi fırlat
        if (error instanceof TRPCError) {
          throw error
        }

        // Diğer hataları genel hata olarak döndür
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Giriş işlemi sırasında hata oluştu',
        })
      }
    }),

  // Mevcut kullanıcı bilgilerini getir
  me: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.prisma.user.findUnique({
      where: { id: ctx.user.id },
      include: {
        company: true,
        branch: true,
      },
    })

    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kullanıcı bulunamadı',
      })
    }

    return {
      id: user.id,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      company: user.company,
      branch: user.branch,
    }
  }),

  // Şifre değiştir
  changePassword: protectedProcedure
    .input(changePasswordInputSchema)
    .output(successResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const { currentPassword, newPassword } = input

      const user = await ctx.prisma.user.findUnique({
        where: { id: ctx.user.id },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Kullanıcı bulunamadı',
        })
      }

      const isValidPassword = await AuthService.verifyPassword(currentPassword, user.password)
      if (!isValidPassword) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Mevcut şifre yanlış',
        })
      }

      const hashedPassword = await AuthService.hashPassword(newPassword)

      await ctx.prisma.user.update({
        where: { id: ctx.user.id },
        data: { password: hashedPassword },
      })

      return { success: true }
    }),

  // PIN değiştir
  changePin: protectedProcedure
    .input(changePinInputSchema)
    .output(successResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const { pin } = input

      const hashedPin = await AuthService.hashPin(pin)

      await ctx.prisma.user.update({
        where: { id: ctx.user.id },
        data: { pin: hashedPin },
      })

      return { success: true }
    }),

  // Çıkış yap
  logout: protectedProcedure
    .output(successResponseSchema)
    .mutation(async ({ ctx }) => {
      // Context'ten token'ı alabilmek için header'dan parse etmemiz gerekiyor
      // Şimdilik basit bir implementasyon yapalım

      // Kullanıcının tüm aktif sessionlarını sonlandır
      await ctx.prisma.session.updateMany({
        where: {
          userId: ctx.user.id,
          endedAt: null,
        },
        data: {
          endedAt: new Date(),
        },
      })

      return { success: true }
    }),
})

