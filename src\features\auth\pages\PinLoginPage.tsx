import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { trpc } from '../../../lib/trpc-client'
import { LoginLayout } from '../components/LoginLayout'
import { UserSelection } from '../components/UserSelection'
import { PinInput } from '../components/PinInput'
import { PinPad } from '../components/PinPad'
import { StartShiftButton } from '../components/StartShiftButton'
import { useAuthStore } from '../stores/authStore'
import { AleoLogo } from '../../../assets/icons/aleo-logo'
import type { UserForLogin } from '../../../lib/validations/auth'

export function PinLoginPage() {
  const [selectedUser, setSelectedUser] = useState<UserForLogin | null>(null)
  const [pin, setPin] = useState('')
  const [isLoggingIn, setIsLoggingIn] = useState(false)
  
  const { setUser } = useAuthStore()

  // Kullanıcı listesini getir
  const { 
    data: users = [], 
    isLoading: isLoadingUsers,
    error: usersError 
  } = trpc.users.listForLogin.useQuery()

  // PIN login mutation
  const pinLoginMutation = trpc.auth.pinLogin.useMutation({
    onSuccess: (data) => {
      toast.success('Giriş başarılı! Hoş geldiniz.')
      
      // Zustand store'u güncelle
      setUser({
        id: data.user.id,
        companyId: data.user.companyId,
        branchId: data.user.branchId || '',
        username: data.user.username,
        password: '',
        pin: null,
        firstName: data.user.firstName,
        lastName: data.user.lastName,
        email: '',
        phone: null,
        role: data.user.role as any,
        permissions: null,
        active: true,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        company: data.user.company ? {
          id: data.user.company.id,
          name: data.user.company.name,
          taxNumber: '',
          taxOffice: '',
          address: '',
          phone: '',
          email: '',
          logo: null,
          eArchiveUsername: null,
          eArchivePassword: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        } : undefined,
        branch: data.user.branch ? {
          id: data.user.branch.id,
          companyId: data.user.companyId,
          code: data.user.branch.code,
          name: data.user.branch.name,
          address: '',
          phone: '',
          email: null,
          serverIp: null,
          serverPort: null,
          isMainBranch: true,
          openingTime: '09:00',
          closingTime: '23:00',
          cashRegisterId: null,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        } : undefined,
      })
      
      // Token'ı localStorage'a kaydet
      if (data.token) {
        localStorage.setItem('auth-token', data.token)
      }
      
      setIsLoggingIn(false)
    },
    onError: (error) => {
      console.error('PIN login error:', error)
      toast.error(error.message || 'Giriş yapılırken bir hata oluştu')
      setIsLoggingIn(false)
    }
  })

  // Kullanıcı seçimi
  const handleUserSelect = (user: UserForLogin) => {
    setSelectedUser(user)
    setPin('') // PIN'i sıfırla
  }

  // PIN girişi
  const handleNumberClick = (number: string) => {
    if (pin.length < 4) {
      setPin(prev => prev + number)
    }
  }

  const handleDeleteClick = () => {
    setPin(prev => prev.slice(0, -1))
  }

  // Giriş işlemi
  const handleStartShift = async () => {
    if (!selectedUser) {
      toast.error('Lütfen bir kullanıcı seçin')
      return
    }

    if (pin.length !== 4) {
      toast.error('PIN 4 haneli olmalıdır')
      return
    }

    setIsLoggingIn(true)
    
    try {
      await pinLoginMutation.mutateAsync({
        userId: selectedUser.id,
        pin: pin
      })
    } catch (error) {
      // Error handling is done in onError callback
    }
  }

  // Hata durumunda toast göster
  useEffect(() => {
    if (usersError) {
      toast.error('Kullanıcı listesi yüklenirken hata oluştu')
    }
  }, [usersError])

  return (
    <LoginLayout>
      <div className="flex flex-col h-full items-center justify-center px-[72px] py-8 gap-8">
        <div className="w-full max-w-[400px] flex flex-col items-center gap-8">
          {/* Logo */}
          <div className="flex-shrink-0">
            <AleoLogo className="w-[73.556px] h-10" />
          </div>

          {/* Başlık */}
          <h1 className="font-inter font-bold text-[28px] leading-[42px] text-[#025cca] text-center tracking-[-0.28px] flex-shrink-0">
            Çalışan Girişi
          </h1>

          {/* Kullanıcı Seçimi */}
          <div className="w-full flex-shrink-0">
            <UserSelection
              users={users}
              selectedUser={selectedUser}
              onUserSelect={handleUserSelect}
              isLoading={isLoadingUsers}
            />
          </div>

          {/* PIN Girişi */}
          <div className="flex flex-col gap-6 items-center flex-shrink-0">
            <div className="flex flex-col gap-3 items-center">
              <p className="font-inter font-normal text-[14px] leading-[21px] text-[#636566] text-center">
                Kendinizi doğrulamak için PIN'inizi girin.
              </p>
              <PinInput pin={pin} maxLength={4} />
            </div>

            <PinPad
              onNumberClick={handleNumberClick}
              onDeleteClick={handleDeleteClick}
              disabled={!selectedUser || isLoggingIn}
            />
          </div>

          {/* Start Shift Button */}
          <div className="w-full flex-shrink-0">
            <StartShiftButton
              onClick={handleStartShift}
              disabled={!selectedUser || pin.length !== 4}
              loading={isLoggingIn}
            />
          </div>
        </div>
      </div>
    </LoginLayout>
  )
}
