import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { Decimal } from 'decimal.js'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Para formatı
export function formatCurrency(amount: number | string | Decimal, currency = '₺'): string {
  const value = typeof amount === 'string' ? parseFloat(amount) : 
                amount instanceof Decimal ? amount.toNumber() : amount
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value).replace('₺', currency)
}

// Tarih formatı
export function formatDate(date: Date | string, format: 'short' | 'long' | 'time' = 'short'): string {
  const d = typeof date === 'string' ? new Date(date) : date
  
  switch (format) {
    case 'short':
      return d.toLocaleDateString('tr-TR')
    case 'long':
      return d.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
    case 'time':
      return d.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit',
      })
    default:
      return d.toLocaleDateString('tr-TR')
  }
}

// Decimal hesaplama yardımcıları
export const decimal = {
  add: (a: number | string | Decimal, b: number | string | Decimal): Decimal => {
    return new Decimal(a).plus(new Decimal(b))
  },
  subtract: (a: number | string | Decimal, b: number | string | Decimal): Decimal => {
    return new Decimal(a).minus(new Decimal(b))
  },
  multiply: (a: number | string | Decimal, b: number | string | Decimal): Decimal => {
    return new Decimal(a).mul(new Decimal(b))
  },
  divide: (a: number | string | Decimal, b: number | string | Decimal): Decimal => {
    return new Decimal(a).div(new Decimal(b))
  },
  round: (value: number | string | Decimal, decimals = 2): Decimal => {
    return new Decimal(value).toDecimalPlaces(decimals)
  },
  toFixed: (value: number | string | Decimal, decimals = 2): string => {
    return new Decimal(value).toFixed(decimals)
  },
}

// Hata mesajı çıkarma
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'object' && error !== null && 'message' in error) {
    return String(error.message)
  }
  
  return 'Bilinmeyen bir hata oluştu'
}

// Kullanıcı dostu hata mesajları
export function getUserMessage(error: unknown): string {
  const message = getErrorMessage(error)
  
  // Yaygın hata mesajlarını Türkçe'ye çevir
  const translations: Record<string, string> = {
    'UNAUTHORIZED': 'Giriş yapmanız gerekiyor',
    'FORBIDDEN': 'Bu işlem için yetkiniz bulunmuyor',
    'NOT_FOUND': 'Kayıt bulunamadı',
    'CONFLICT': 'Bu kayıt zaten mevcut',
    'BAD_REQUEST': 'Geçersiz istek',
    'INTERNAL_SERVER_ERROR': 'Sunucu hatası oluştu',
    'Network Error': 'Bağlantı hatası',
    'timeout': 'İşlem zaman aşımına uğradı',
  }
  
  for (const [key, value] of Object.entries(translations)) {
    if (message.includes(key)) {
      return value
    }
  }
  
  return message
}

// Debounce fonksiyonu
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

// Local storage yardımcıları
export const storage = {
  get: <T>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue ?? null
    } catch {
      return defaultValue ?? null
    }
  },
  
  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Storage set error:', error)
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Storage remove error:', error)
    }
  },
  
  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Storage clear error:', error)
    }
  },
}

// Klavye kısayol yardımcısı
export function isHotkey(event: KeyboardEvent, hotkey: string): boolean {
  const keys = hotkey.toLowerCase().split('+')
  const eventKey = event.key.toLowerCase()
  
  const modifiers = {
    ctrl: event.ctrlKey || event.metaKey,
    alt: event.altKey,
    shift: event.shiftKey,
  }
  
  // Son key actual key olmalı
  const actualKey = keys[keys.length - 1]
  if (eventKey !== actualKey) return false
  
  // Modifier kontrolü
  const requiredModifiers = keys.slice(0, -1)
  
  return requiredModifiers.every(mod => {
    if (mod === 'ctrl') return modifiers.ctrl
    if (mod === 'alt') return modifiers.alt
    if (mod === 'shift') return modifiers.shift
    return false
  }) && Object.entries(modifiers).every(([key, pressed]) => {
    return requiredModifiers.includes(key) === pressed
  })
}

