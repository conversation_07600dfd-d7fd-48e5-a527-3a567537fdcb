import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist-react',
    emptyOutDir: true,
  },
  server: {
    port: 3000,
    host: 'localhost',
    watch: {
      usePolling: true,
    },
  },
  base: './',
})

