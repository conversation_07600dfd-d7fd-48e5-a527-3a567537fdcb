import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router, publicProcedure, protectedProcedure, adminProcedure } from '../trpc'
import { AuthService } from '../services/authService'
import {
  userListFilterSchema,
  usersForLoginListSchema,
  updateUserPinInputSchema,
  successResponseSchema,
} from '../../lib/validations/auth'

export const usersRouter = router({
  /**
   * PIN Login ekranında gösterilecek aktif kullanıcıların listesini döndürür
   * 
   * Bu prosedür:
   * - Sadece aktif ve PIN'i olan kullanıcıları getirir
   * - Güvenlik için sadece gerekli bilgileri döndürür (şifre, PIN vb. döndürmez)
   * - Şirket ve şube filtrelemesi yapabilir
   * - Kullanıcıları alfabetik sıraya göre döndürür
   */
  listForLogin: publicProcedure
    .input(userListFilterSchema)
    .output(usersForLoginListSchema)
    .query(async ({ input, ctx }) => {
      try {
        const users = await AuthService.getActiveUsersForLogin(
          input?.companyId,
          input?.branchId
        )

        return users
      } catch (error) {
        console.error('Error fetching users for login:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Kullanıcılar getirilirken hata oluştu',
        })
      }
    }),

  /**
   * Kullanıcı detaylarını getirir (korumalı endpoint)
   */
  getById: protectedProcedure
    .input(z.object({ id: z.string().cuid() }))
    .query(async ({ input, ctx }) => {
      const user = await ctx.prisma.user.findUnique({
        where: { id: input.id },
        include: {
          company: true,
          branch: true,
        },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Kullanıcı bulunamadı',
        })
      }

      // Yetki kontrolü - sadece kendi bilgilerini veya admin yetkisi olanlar görebilir
      if (
        user.id !== ctx.user.id &&
        !['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'].includes(ctx.user.role)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Bu kullanıcının bilgilerini görme yetkiniz yok',
        })
      }

      return AuthService.getSafeUserInfo(user)
    }),

  /**
   * Şube kullanıcılarını listeler (admin yetkisi gerekli)
   */
  listByBranch: adminProcedure
    .input(z.object({
      branchId: z.string().cuid(),
      includeInactive: z.boolean().default(false),
    }))
    .query(async ({ input, ctx }) => {
      const users = await ctx.prisma.user.findMany({
        where: {
          branchId: input.branchId,
          active: input.includeInactive ? undefined : true,
        },
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: [
          { firstName: 'asc' },
          { lastName: 'asc' },
        ],
      })

      return users.map(user => AuthService.getSafeUserInfo(user))
    }),

  /**
   * Şirket kullanıcılarını listeler (admin yetkisi gerekli)
   */
  listByCompany: adminProcedure
    .input(z.object({
      companyId: z.string().cuid(),
      includeInactive: z.boolean().default(false),
    }))
    .query(async ({ input, ctx }) => {
      const users = await ctx.prisma.user.findMany({
        where: {
          companyId: input.companyId,
          active: input.includeInactive ? undefined : true,
        },
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: [
          { firstName: 'asc' },
          { lastName: 'asc' },
        ],
      })

      return users.map(user => AuthService.getSafeUserInfo(user))
    }),

  /**
   * Kullanıcı PIN'ini günceller
   */
  updatePin: protectedProcedure
    .input(updateUserPinInputSchema)
    .output(successResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const targetUserId = input.userId || ctx.user.id

      // Yetki kontrolü - sadece kendi PIN'ini veya admin yetkisi olanlar değiştirebilir
      if (
        targetUserId !== ctx.user.id &&
        !['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'].includes(ctx.user.role)
      ) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Bu kullanıcının PIN\'ini değiştirme yetkiniz yok',
        })
      }

      // PIN'i hashle
      const hashedPin = await AuthService.hashPin(input.pin)

      // Kullanıcıyı güncelle
      await ctx.prisma.user.update({
        where: { id: targetUserId },
        data: { pin: hashedPin },
      })

      return { success: true }
    }),

  /**
   * Kullanıcı durumunu değiştirir (aktif/pasif)
   */
  toggleStatus: adminProcedure
    .input(z.object({
      userId: z.string().cuid(),
      active: z.boolean(),
    }))
    .output(successResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const user = await ctx.prisma.user.findUnique({
        where: { id: input.userId },
      })

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Kullanıcı bulunamadı',
        })
      }

      // Kullanıcı durumunu güncelle
      await ctx.prisma.user.update({
        where: { id: input.userId },
        data: { active: input.active },
      })

      // Eğer kullanıcı pasif hale getiriliyorsa, aktif sessionlarını sonlandır
      if (!input.active) {
        await ctx.prisma.session.updateMany({
          where: {
            userId: input.userId,
            endedAt: null,
          },
          data: {
            endedAt: new Date(),
          },
        })
      }

      return { success: true }
    }),
})
