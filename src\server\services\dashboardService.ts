import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import { OrderStatus, PaymentStatus, ProductUnit } from '@prisma/client'
import type {
  OverviewStatsInput,
  OverviewStatsOutput,
  PopularDishesInput,
  PopularDishesOutput,
  OutOfStockInput,
  OutOfStockOutput,
  OrdersListInput,
  OrdersListOutput,
  PercentageChange,
} from '../../lib/validations/dashboard'

/**
 * DashboardService - Dashboard sayfası için iş mantığı ve veri hesaplamaları
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Özet istatistikleri hesaplama (kazanç, sipariş sayıları)
 * - Popüler ürünleri listeleme
 * - Stok kritik ürünleri bulma
 * - Sipariş listelerini filtreleme
 * - Yüzde değişim hesaplamaları
 */
export class DashboardService {
  /**
   * <PERSON><PERSON><PERSON><PERSON> değ<PERSON><PERSON><PERSON> hesaplar
   * @param current - <PERSON><PERSON>nce<PERSON> değer
   * @param previous - <PERSON><PERSON><PERSON> değer
   * @returns Yüzde değişim objesi
   */
  static calculatePercentageChange(current: number, previous: number): PercentageChange {
    if (previous === 0) {
      return {
        value: current > 0 ? '+100%' : '0%',
        isPositive: current > 0,
      }
    }

    const change = ((current - previous) / previous) * 100
    const isPositive = change >= 0
    const formattedChange = `${isPositive ? '+' : ''}${change.toFixed(1)}%`

    return {
      value: formattedChange,
      isPositive,
    }
  }

  /**
   * Belirli bir tarih için günün başlangıç ve bitiş zamanlarını döndürür
   * @param date - Tarih
   * @returns Başlangıç ve bitiş zamanları
   */
  static getDayRange(date: Date): { startOfDay: Date; endOfDay: Date } {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    return { startOfDay, endOfDay }
  }

  /**
   * Önceki günün tarih aralığını döndürür
   * @param date - Referans tarih
   * @returns Önceki günün başlangıç ve bitiş zamanları
   */
  static getPreviousDayRange(date: Date): { startOfDay: Date; endOfDay: Date } {
    const previousDay = new Date(date)
    previousDay.setDate(previousDay.getDate() - 1)
    return this.getDayRange(previousDay)
  }

  /**
   * Dashboard özet istatistiklerini getirir
   * @param input - Filtre parametreleri
   * @param companyId - Şirket ID'si
   * @param userBranchId - Kullanıcının şube ID'si (null ise tüm şubeler)
   * @returns Özet istatistikler
   */
  static async getOverviewStats(
    input: OverviewStatsInput,
    companyId: string,
    userBranchId?: string
  ): Promise<OverviewStatsOutput> {
    const targetDate = input.date || new Date()
    const branchId = input.branchId || userBranchId

    const { startOfDay, endOfDay } = this.getDayRange(targetDate)
    const { startOfDay: prevStartOfDay, endOfDay: prevEndOfDay } = this.getPreviousDayRange(targetDate)

    // Bugünün satış verilerini getir
    const todayOrders = await prisma.order.findMany({
      where: {
        ...(branchId && { branchId }),
        orderedAt: {
          gte: startOfDay,
          lte: endOfDay,
        },
        status: { not: OrderStatus.CANCELLED },
      },
      select: {
        totalAmount: true,
        status: true,
        paymentStatus: true,
      },
    })

    // Dünün satış verilerini getir
    const yesterdayOrders = await prisma.order.findMany({
      where: {
        ...(branchId && { branchId }),
        orderedAt: {
          gte: prevStartOfDay,
          lte: prevEndOfDay,
        },
        status: { not: OrderStatus.CANCELLED },
      },
      select: {
        totalAmount: true,
      },
    })

    // Toplam kazanç hesapla (sadece ödenen siparişler)
    const todayEarning = todayOrders
      .filter(order => order.paymentStatus === PaymentStatus.PAID)
      .reduce((sum, order) => sum.plus(order.totalAmount), new Decimal(0))

    const yesterdayEarning = yesterdayOrders
      .reduce((sum, order) => sum.plus(order.totalAmount), new Decimal(0))

    // Devam eden siparişler (PREPARING veya PENDING)
    const todayInProgress = todayOrders.filter(order => 
      order.status === OrderStatus.PREPARING || order.status === OrderStatus.PENDING
    ).length

    const yesterdayInProgressOrders = await prisma.order.count({
      where: {
        ...(branchId && { branchId }),
        orderedAt: {
          gte: prevStartOfDay,
          lte: prevEndOfDay,
        },
        status: { in: [OrderStatus.PREPARING, OrderStatus.PENDING] },
      },
    })

    // Bekleme listesi (rezerve veya dolu masalar)
    const waitingTables = await prisma.table.count({
      where: {
        ...(branchId && { branchId }),
        status: { in: ['RESERVED', 'OCCUPIED'] },
        active: true,
      },
    })

    // Dün aynı saatte bekleme listesi
    const yesterdayWaitingTables = await prisma.table.count({
      where: {
        ...(branchId && { branchId }),
        status: { in: ['RESERVED', 'OCCUPIED'] },
        active: true,
      },
    })

    return {
      totalEarning: {
        amount: todayEarning.toFixed(2),
        change: this.calculatePercentageChange(
          todayEarning.toNumber(),
          yesterdayEarning.toNumber()
        ),
      },
      inProgressOrdersCount: {
        count: todayInProgress,
        change: this.calculatePercentageChange(todayInProgress, yesterdayInProgressOrders),
      },
      waitingListCount: {
        count: waitingTables,
        change: this.calculatePercentageChange(waitingTables, yesterdayWaitingTables),
      },
    }
  }

  /**
   * Popüler yemekleri getirir
   * @param input - Filtre parametreleri
   * @param companyId - Şirket ID'si
   * @param userBranchId - Kullanıcının şube ID'si
   * @returns Popüler yemekler listesi
   */
  static async getPopularDishes(
    input: PopularDishesInput,
    companyId: string,
    userBranchId?: string
  ): Promise<PopularDishesOutput> {
    const branchId = input.branchId || userBranchId

    const popularDishes = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          ...(branchId && { branchId }),
          orderedAt: {
            gte: input.startDate,
            lte: input.endDate,
          },
          status: { not: OrderStatus.CANCELLED },
        },
      },
      _sum: {
        quantity: true,
      },
      orderBy: {
        _sum: {
          quantity: 'desc',
        },
      },
      take: input.limit,
    })

    // Ürün detaylarını getir
    const productIds = popularDishes.map(dish => dish.productId)
    const products = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        companyId,
        active: true,
      },
      select: {
        id: true,
        name: true,
        image: true,
      },
    })

    // Sonuçları birleştir
    return popularDishes.map(dish => {
      const product = products.find(p => p.id === dish.productId)
      return {
        productId: dish.productId,
        name: product?.name || 'Bilinmeyen Ürün',
        orderCount: dish._sum.quantity?.toNumber() || 0,
        image: product?.image,
      }
    })
  }

  /**
   * Stok kritik ürünleri getirir
   * @param input - Filtre parametreleri
   * @param companyId - Şirket ID'si
   * @returns Stok kritik ürünler listesi
   */
  static async getOutOfStockItems(
    input: OutOfStockInput,
    companyId: string
  ): Promise<OutOfStockOutput> {
    // Stok takibi yapılan ve kritik seviyede olan ürünleri getir
    const criticalProducts = await prisma.product.findMany({
      where: {
        companyId,
        trackStock: true,
        active: true,
        available: true,
      },
      include: {
        inventoryItems: {
          where: {
            active: true,
          },
          select: {
            currentStock: true,
            criticalLevel: true,
          },
        },
      },
      take: input.limit * 2, // Daha fazla getir, filtreleyeceğiz
    })

    // Kritik seviyede olanları filtrele
    const outOfStockItems = criticalProducts
      .filter(product => {
        if (product.inventoryItems.length === 0) return false
        
        const inventory = product.inventoryItems[0]
        if (!inventory.criticalLevel) return false
        
        return inventory.currentStock.lte(inventory.criticalLevel)
      })
      .slice(0, input.limit)
      .map(product => {
        const inventory = product.inventoryItems[0]
        return {
          productId: product.id,
          name: product.name,
          currentStock: inventory.currentStock.toFixed(3),
          unit: this.getUnitDisplayName(product.unit),
          criticalLevel: inventory.criticalLevel?.toFixed(3),
          availableInfo: 'Available: Tomorrow', // Placeholder
          image: product.image,
        }
      })

    return outOfStockItems
  }

  /**
   * Birim adını Türkçe'ye çevirir
   * @param unit - Ürün birimi
   * @returns Türkçe birim adı
   */
  static getUnitDisplayName(unit: ProductUnit): string {
    const unitMap: Record<ProductUnit, string> = {
      PIECE: 'Adet',
      KG: 'Kg',
      GRAM: 'Gram',
      LITER: 'Litre',
      ML: 'ml',
      PORTION: 'Porsiyon',
    }
    return unitMap[unit] || unit
  }

  /**
   * Sipariş listesini getirir
   * @param input - Filtre parametreleri
   * @param companyId - Şirket ID'si
   * @param userBranchId - Kullanıcının şube ID'si
   * @returns Sipariş listesi
   */
  static async getOrdersList(
    input: OrdersListInput,
    companyId: string,
    userBranchId?: string
  ): Promise<OrdersListOutput> {
    const branchId = input.branchId || userBranchId

    // Durum filtresini belirle
    let statusFilter: any = {}
    if (input.status === 'inProgress') {
      statusFilter = {
        status: { in: [OrderStatus.PREPARING, OrderStatus.PENDING, OrderStatus.CONFIRMED, OrderStatus.READY] },
      }
    } else if (input.status === 'waitingForPayment') {
      statusFilter = {
        paymentStatus: { in: [PaymentStatus.UNPAID, PaymentStatus.PARTIAL] },
        status: { not: OrderStatus.CANCELLED },
      }
    }

    const orders = await prisma.order.findMany({
      where: {
        ...(branchId && { branchId }),
        ...statusFilter,
        ...(input.search && {
          OR: [
            { orderNumber: { contains: input.search, mode: 'insensitive' } },
            { customerName: { contains: input.search, mode: 'insensitive' } },
            { table: { number: { contains: input.search, mode: 'insensitive' } } },
          ],
        }),
      },
      include: {
        table: {
          select: {
            number: true,
          },
        },
        items: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        orderedAt: 'desc',
      },
      take: input.limit,
    })

    return orders.map(order => {
      const { status: orderStatus, paymentStatus } = order
      
      // Durum ve renk belirleme
      let status = 'Unknown'
      let statusColor: 'green' | 'orange' | 'blue' | 'red' = 'blue'
      let hasPayButton = false

      if (paymentStatus === PaymentStatus.UNPAID || paymentStatus === PaymentStatus.PARTIAL) {
        status = 'Pay Now'
        statusColor = 'blue'
        hasPayButton = true
      } else if (orderStatus === OrderStatus.READY) {
        status = 'Ready'
        statusColor = 'green'
      } else if (orderStatus === OrderStatus.PREPARING) {
        status = 'Cooking Now'
        statusColor = 'orange'
      } else if (orderStatus === OrderStatus.PENDING || orderStatus === OrderStatus.CONFIRMED) {
        status = 'In the Kitchen'
        statusColor = 'orange'
      }

      return {
        orderId: order.id,
        orderNumber: order.table?.number || order.orderNumber.split('-')[1] || 'N/A',
        customerName: order.customerName,
        totalItems: order.items.length,
        status,
        statusColor,
        tableNumber: order.table?.number,
        hasPayButton,
      }
    })
  }
}
