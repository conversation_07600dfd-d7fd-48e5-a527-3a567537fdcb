import { z } from 'zod'
import { ProductUnit } from '@prisma/client'

// ==================== COMMON SCHEMAS ====================

/**
 * Pagination şeması
 */
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

/**
 * ID şeması
 */
export const idSchema = z.object({
  id: z.string().cuid('Geçerli bir ID gerekli'),
})

/**
 * Decimal string şeması (para ve miktar için)
 */
export const decimalStringSchema = z.string().regex(/^\d+(\.\d{1,2})?$/, 'Geçerli bir sayı formatı gerekli')

// ==================== CATEGORY SCHEMAS ====================

/**
 * Kategori oluşturma input şeması
 */
export const categoryCreateInputSchema = z.object({
  name: z.string().min(1, '<PERSON>gori adı gerekli').max(100, 'Kategori adı en fazla 100 karakter olabilir'),
  description: z.string().max(500, 'Açıklama en fazla 500 karakter olabilir').optional(),
  image: z.string().url('Geçerli bir resim URL\'si gerekli').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Geçerli bir hex renk kodu gerekli').optional(),
  icon: z.string().max(50, 'İkon adı en fazla 50 karakter olabilir').optional(),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
  parentId: z.string().cuid().optional(),
  printerGroupId: z.string().cuid().optional(),
  preparationTime: z.number().int().min(0).max(999).optional(),
})

/**
 * Kategori güncelleme input şeması
 */
export const categoryUpdateInputSchema = categoryCreateInputSchema.partial().merge(idSchema)

/**
 * Kategori listesi input şeması
 */
export const categoryListInputSchema = z.object({
  parentId: z.string().cuid().optional(),
  active: z.boolean().optional(),
  includeChildren: z.boolean().default(false),
}).merge(paginationSchema.partial())

/**
 * Kategori output şeması
 */
export const categoryOutputSchema: z.ZodType<{
  id: string
  name: string
  description: string | null
  image: string | null
  color: string | null
  icon: string | null
  displayOrder: number
  active: boolean
  parentId: string | null
  printerGroupId: string | null
  preparationTime: number | null
  createdAt: Date
  updatedAt: Date
  children?: Array<{
    id: string
    name: string
    description: string | null
    image: string | null
    color: string | null
    icon: string | null
    displayOrder: number
    active: boolean
    parentId: string | null
    printerGroupId: string | null
    preparationTime: number | null
    createdAt: Date
    updatedAt: Date
    children?: any[]
    _count?: {
      products: number
      children: number
    }
  }>
  _count?: {
    products: number
    children: number
  }
}> = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  image: z.string().nullable(),
  color: z.string().nullable(),
  icon: z.string().nullable(),
  displayOrder: z.number(),
  active: z.boolean(),
  parentId: z.string().nullable(),
  printerGroupId: z.string().nullable(),
  preparationTime: z.number().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
  children: z.array(z.lazy((): z.ZodTypeAny => categoryOutputSchema)).optional(),
  _count: z.object({
    products: z.number(),
    children: z.number(),
  }).optional(),
})

/**
 * Kategori listesi output şeması
 */
export const categoryListOutputSchema = z.object({
  categories: z.array(categoryOutputSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// ==================== PRODUCT SCHEMAS ====================

/**
 * Ürün oluşturma input şeması
 */
export const productCreateInputSchema = z.object({
  categoryId: z.string().cuid('Geçerli bir kategori ID\'si gerekli'),
  code: z.string().min(1, 'Ürün kodu gerekli').max(50, 'Ürün kodu en fazla 50 karakter olabilir'),
  barcode: z.string().max(50, 'Barkod en fazla 50 karakter olabilir').optional(),
  name: z.string().min(1, 'Ürün adı gerekli').max(200, 'Ürün adı en fazla 200 karakter olabilir'),
  description: z.string().max(1000, 'Açıklama en fazla 1000 karakter olabilir').optional(),
  image: z.string().url('Geçerli bir resim URL\'si gerekli').optional(),
  basePrice: decimalStringSchema,
  taxId: z.string().cuid('Geçerli bir vergi ID\'si gerekli'),
  trackStock: z.boolean().default(false),
  unit: z.nativeEnum(ProductUnit).default(ProductUnit.PIECE),
  criticalStock: decimalStringSchema.optional(),
  available: z.boolean().default(true),
  sellable: z.boolean().default(true),
  preparationTime: z.number().int().min(0).max(999).optional(),
  hasVariants: z.boolean().default(false),
  hasModifiers: z.boolean().default(false),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
})

/**
 * Ürün güncelleme input şeması
 */
export const productUpdateInputSchema = productCreateInputSchema.partial().merge(idSchema)

/**
 * Ürün listesi input şeması
 */
export const productListInputSchema = z.object({
  categoryId: z.string().cuid().optional(),
  search: z.string().max(100).optional(),
  active: z.boolean().optional(),
  available: z.boolean().optional(),
  sellable: z.boolean().optional(),
  hasVariants: z.boolean().optional(),
  hasModifiers: z.boolean().optional(),
  trackStock: z.boolean().optional(),
  includeVariants: z.boolean().default(false),
  includeModifiers: z.boolean().default(false),
}).merge(paginationSchema.partial())

/**
 * Ürün output şeması
 */
export const productOutputSchema = z.object({
  id: z.string(),
  categoryId: z.string(),
  code: z.string(),
  barcode: z.string().nullable(),
  name: z.string(),
  description: z.string().nullable(),
  image: z.string().nullable(),
  basePrice: z.string(),
  taxId: z.string(),
  trackStock: z.boolean(),
  unit: z.nativeEnum(ProductUnit),
  criticalStock: z.string().nullable(),
  available: z.boolean(),
  sellable: z.boolean(),
  preparationTime: z.number().nullable(),
  hasVariants: z.boolean(),
  hasModifiers: z.boolean(),
  displayOrder: z.number(),
  active: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  category: z.object({
    id: z.string(),
    name: z.string(),
    color: z.string().nullable(),
    icon: z.string().nullable(),
  }).optional(),
  tax: z.object({
    id: z.string(),
    name: z.string(),
    rate: z.string(),
    code: z.string(),
  }).optional(),
  variants: z.array(z.object({
    id: z.string(),
    name: z.string(),
    code: z.string(),
    price: z.string(),
    displayOrder: z.number(),
    active: z.boolean(),
  })).optional(),
  modifierGroups: z.array(z.object({
    id: z.string(),
    name: z.string(),
    minSelection: z.number(),
    maxSelection: z.number(),
    required: z.boolean(),
    displayOrder: z.number(),
    active: z.boolean(),
    modifiers: z.array(z.object({
      id: z.string(),
      name: z.string(),
      price: z.string(),
      displayOrder: z.number(),
      active: z.boolean(),
    })).optional(),
  })).optional(),
})

/**
 * Ürün listesi output şeması
 */
export const productListOutputSchema = z.object({
  products: z.array(productOutputSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// ==================== PRODUCT VARIANT SCHEMAS ====================

/**
 * Ürün varyantı oluşturma input şeması
 */
export const productVariantCreateInputSchema = z.object({
  productId: z.string().cuid('Geçerli bir ürün ID\'si gerekli'),
  name: z.string().min(1, 'Varyant adı gerekli').max(100, 'Varyant adı en fazla 100 karakter olabilir'),
  code: z.string().min(1, 'Varyant kodu gerekli').max(20, 'Varyant kodu en fazla 20 karakter olabilir'),
  price: decimalStringSchema,
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
})

/**
 * Ürün varyantı güncelleme input şeması
 */
export const productVariantUpdateInputSchema = productVariantCreateInputSchema.partial().merge(idSchema)

/**
 * Ürün varyantı output şeması
 */
export const productVariantOutputSchema = z.object({
  id: z.string(),
  productId: z.string(),
  name: z.string(),
  code: z.string(),
  price: z.string(),
  displayOrder: z.number(),
  active: z.boolean(),
  product: z.object({
    id: z.string(),
    name: z.string(),
    code: z.string(),
  }).optional(),
})

// ==================== MODIFIER SCHEMAS ====================

/**
 * Modifiyer grubu oluşturma input şeması
 */
export const modifierGroupCreateInputSchema = z.object({
  name: z.string().min(1, 'Modifiyer grubu adı gerekli').max(100, 'Modifiyer grubu adı en fazla 100 karakter olabilir'),
  minSelection: z.number().int().min(0).default(0),
  maxSelection: z.number().int().min(1).default(1),
  required: z.boolean().default(false),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
})

/**
 * Modifiyer grubu güncelleme input şeması
 */
export const modifierGroupUpdateInputSchema = modifierGroupCreateInputSchema.partial().merge(idSchema)

/**
 * Modifiyer oluşturma input şeması
 */
export const modifierCreateInputSchema = z.object({
  groupId: z.string().cuid('Geçerli bir modifiyer grubu ID\'si gerekli'),
  name: z.string().min(1, 'Modifiyer adı gerekli').max(100, 'Modifiyer adı en fazla 100 karakter olabilir'),
  price: decimalStringSchema.default('0'),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
})

/**
 * Modifiyer güncelleme input şeması
 */
export const modifierUpdateInputSchema = modifierCreateInputSchema.partial().merge(idSchema)

/**
 * Modifiyer output şeması
 */
export const modifierOutputSchema = z.object({
  id: z.string(),
  groupId: z.string(),
  name: z.string(),
  price: z.string(),
  displayOrder: z.number(),
  active: z.boolean(),
})

/**
 * Modifiyer grubu output şeması
 */
export const modifierGroupOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  minSelection: z.number(),
  maxSelection: z.number(),
  required: z.boolean(),
  displayOrder: z.number(),
  active: z.boolean(),
  modifiers: z.array(modifierOutputSchema).optional(),
  _count: z.object({
    modifiers: z.number(),
    products: z.number(),
  }).optional(),
})

/**
 * Modifiyer grubu listesi output şeması
 */
export const modifierGroupListOutputSchema = z.object({
  modifierGroups: z.array(modifierGroupOutputSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// ==================== PRODUCT MODIFIER RELATION SCHEMAS ====================

/**
 * Ürüne modifiyer grubu ekleme input şeması
 */
export const productAddModifierGroupInputSchema = z.object({
  productId: z.string().cuid('Geçerli bir ürün ID\'si gerekli'),
  modifierGroupId: z.string().cuid('Geçerli bir modifiyer grubu ID\'si gerekli'),
  displayOrder: z.number().int().min(0).default(0),
})

/**
 * Üründen modifiyer grubu kaldırma input şeması
 */
export const productRemoveModifierGroupInputSchema = z.object({
  productId: z.string().cuid('Geçerli bir ürün ID\'si gerekli'),
  modifierGroupId: z.string().cuid('Geçerli bir modifiyer grubu ID\'si gerekli'),
})

// ==================== TAX SCHEMAS ====================

/**
 * Vergi oluşturma input şeması
 */
export const taxCreateInputSchema = z.object({
  name: z.string().min(1, 'Vergi adı gerekli').max(100, 'Vergi adı en fazla 100 karakter olabilir'),
  rate: decimalStringSchema,
  code: z.string().min(1, 'Vergi kodu gerekli').max(20, 'Vergi kodu en fazla 20 karakter olabilir'),
  isDefault: z.boolean().default(false),
  active: z.boolean().default(true),
})

/**
 * Vergi güncelleme input şeması
 */
export const taxUpdateInputSchema = taxCreateInputSchema.partial().merge(idSchema)

/**
 * Vergi output şeması
 */
export const taxOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  rate: z.string(),
  code: z.string(),
  isDefault: z.boolean(),
  active: z.boolean(),
  _count: z.object({
    products: z.number(),
  }).optional(),
})

/**
 * Vergi listesi output şeması
 */
export const taxListOutputSchema = z.object({
  taxes: z.array(taxOutputSchema),
  total: z.number(),
})

// ==================== PRICE OVERRIDE SCHEMAS ====================

/**
 * Fiyat geçersiz kılma oluşturma input şeması
 */
export const priceOverrideCreateInputSchema = z.object({
  productId: z.string().cuid('Geçerli bir ürün ID\'si gerekli'),
  branchId: z.string().cuid().optional(),
  price: decimalStringSchema,
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Geçerli bir saat formatı gerekli (HH:MM)').optional(),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Geçerli bir saat formatı gerekli (HH:MM)').optional(),
  daysOfWeek: z.array(z.number().int().min(1).max(7)).optional(),
  active: z.boolean().default(true),
})

/**
 * Fiyat geçersiz kılma güncelleme input şeması
 */
export const priceOverrideUpdateInputSchema = priceOverrideCreateInputSchema.partial().merge(idSchema)

/**
 * Fiyat geçersiz kılma listesi input şeması
 */
export const priceOverrideListInputSchema = z.object({
  productId: z.string().cuid().optional(),
  branchId: z.string().cuid().optional(),
  active: z.boolean().optional(),
  includeExpired: z.boolean().default(false),
}).merge(paginationSchema.partial())

/**
 * Fiyat geçersiz kılma output şeması
 */
export const priceOverrideOutputSchema = z.object({
  id: z.string(),
  productId: z.string(),
  branchId: z.string().nullable(),
  price: z.string(),
  startDate: z.date().nullable(),
  endDate: z.date().nullable(),
  startTime: z.string().nullable(),
  endTime: z.string().nullable(),
  daysOfWeek: z.array(z.number()),
  active: z.boolean(),
  product: z.object({
    id: z.string(),
    name: z.string(),
    code: z.string(),
    basePrice: z.string(),
  }).optional(),
  branch: z.object({
    id: z.string(),
    name: z.string(),
    code: z.string(),
  }).optional(),
})

/**
 * Fiyat geçersiz kılma listesi output şeması
 */
export const priceOverrideListOutputSchema = z.object({
  priceOverrides: z.array(priceOverrideOutputSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
})

// ==================== SUCCESS RESPONSE SCHEMAS ====================

/**
 * Genel başarı response şeması
 */
export const successResponseSchema = z.object({
  success: z.literal(true),
  message: z.string().optional(),
})

/**
 * ID ile başarı response şeması
 */
export const successWithIdResponseSchema = z.object({
  success: z.literal(true),
  id: z.string(),
  message: z.string().optional(),
})

// ==================== TYPE EXPORTS ====================

export type CategoryCreateInput = z.infer<typeof categoryCreateInputSchema>
export type CategoryUpdateInput = z.infer<typeof categoryUpdateInputSchema>
export type CategoryListInput = z.infer<typeof categoryListInputSchema>
export type CategoryOutput = z.infer<typeof categoryOutputSchema>
export type CategoryListOutput = z.infer<typeof categoryListOutputSchema>

export type ProductCreateInput = z.infer<typeof productCreateInputSchema>
export type ProductUpdateInput = z.infer<typeof productUpdateInputSchema>
export type ProductListInput = z.infer<typeof productListInputSchema>
export type ProductOutput = z.infer<typeof productOutputSchema>
export type ProductListOutput = z.infer<typeof productListOutputSchema>

export type ProductVariantCreateInput = z.infer<typeof productVariantCreateInputSchema>
export type ProductVariantUpdateInput = z.infer<typeof productVariantUpdateInputSchema>
export type ProductVariantOutput = z.infer<typeof productVariantOutputSchema>

export type ModifierGroupCreateInput = z.infer<typeof modifierGroupCreateInputSchema>
export type ModifierGroupUpdateInput = z.infer<typeof modifierGroupUpdateInputSchema>
export type ModifierGroupOutput = z.infer<typeof modifierGroupOutputSchema>
export type ModifierGroupListOutput = z.infer<typeof modifierGroupListOutputSchema>

export type ModifierCreateInput = z.infer<typeof modifierCreateInputSchema>
export type ModifierUpdateInput = z.infer<typeof modifierUpdateInputSchema>
export type ModifierOutput = z.infer<typeof modifierOutputSchema>

export type ProductAddModifierGroupInput = z.infer<typeof productAddModifierGroupInputSchema>
export type ProductRemoveModifierGroupInput = z.infer<typeof productRemoveModifierGroupInputSchema>

export type TaxCreateInput = z.infer<typeof taxCreateInputSchema>
export type TaxUpdateInput = z.infer<typeof taxUpdateInputSchema>
export type TaxOutput = z.infer<typeof taxOutputSchema>
export type TaxListOutput = z.infer<typeof taxListOutputSchema>

export type PriceOverrideCreateInput = z.infer<typeof priceOverrideCreateInputSchema>
export type PriceOverrideUpdateInput = z.infer<typeof priceOverrideUpdateInputSchema>
export type PriceOverrideListInput = z.infer<typeof priceOverrideListInputSchema>
export type PriceOverrideOutput = z.infer<typeof priceOverrideOutputSchema>
export type PriceOverrideListOutput = z.infer<typeof priceOverrideListOutputSchema>

export type SuccessResponse = z.infer<typeof successResponseSchema>
export type SuccessWithIdResponse = z.infer<typeof successWithIdResponseSchema>
