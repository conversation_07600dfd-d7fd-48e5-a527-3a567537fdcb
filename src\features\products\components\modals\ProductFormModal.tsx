import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../../../../components/ui/dialog'
import { Button } from '../../../../components/ui/button'
import { Input } from '../../../../components/ui/input'
import { Label } from '../../../../components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs'
import { useCategories } from '../../hooks/useCategories'
import { useTaxes } from '../../hooks/useTaxes'

/**
 * Ürün Form Modal Bileşeni
 * 
 * Bu bileşen:
 * - <PERSON><PERSON> ürün ekleme ve mevcut ürün düzenleme
 * - Çok adımlı form yapısı (Genel Bilgiler, Fiyatlandırma, Stok, Modifiyerler)
 * - react-hook-form ve Zod validasyonu
 * - Touch-friendly tasarım
 * - Figma tasarımına uygun modal
 */

// Form validation schema
const productFormSchema = z.object({
  // Genel Bilgiler
  categoryId: z.string().min(1, 'Kategori seçimi zorunludur'),
  code: z.string().min(1, 'Ürün kodu zorunludur').max(50, 'Ürün kodu en fazla 50 karakter olabilir'),
  barcode: z.string().max(50, 'Barkod en fazla 50 karakter olabilir').optional(),
  name: z.string().min(1, 'Ürün adı zorunludur').max(200, 'Ürün adı en fazla 200 karakter olabilir'),
  description: z.string().max(1000, 'Açıklama en fazla 1000 karakter olabilir').optional(),
  image: z.string().url('Geçerli bir resim URL\'si gerekli').optional(),
  
  // Fiyatlandırma
  basePrice: z.string().regex(/^\d+(\.\d{1,2})?$/, 'Geçerli bir fiyat giriniz'),
  taxId: z.string().min(1, 'Vergi oranı seçimi zorunludur'),
  
  // Stok
  trackStock: z.boolean().default(false),
  unit: z.enum(['PIECE', 'KG', 'GRAM', 'LITER', 'ML']).default('PIECE'),
  criticalStock: z.string().regex(/^\d+(\.\d{1,3})?$/, 'Geçerli bir stok miktarı giriniz').optional(),
  
  // Ayarlar
  available: z.boolean().default(true),
  sellable: z.boolean().default(true),
  preparationTime: z.number().int().min(0).max(999).optional(),
  hasVariants: z.boolean().default(false),
  hasModifiers: z.boolean().default(false),
  displayOrder: z.number().int().min(0).default(0),
  active: z.boolean().default(true),
})

type ProductFormData = z.infer<typeof productFormSchema>

interface ProductFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: ProductFormData) => void
  initialData?: Partial<ProductFormData>
  mode: 'create' | 'edit'
  isLoading?: boolean
}

// Mock data kaldırıldı - artık gerçek API kullanılıyor

const unitOptions = [
  { value: 'PIECE', label: 'Adet' },
  { value: 'KG', label: 'Kilogram' },
  { value: 'GRAM', label: 'Gram' },
  { value: 'LITER', label: 'Litre' },
  { value: 'ML', label: 'Mililitre' },
]

export function ProductFormModal({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  mode,
  isLoading = false
}: ProductFormModalProps) {
  const [activeTab, setActiveTab] = useState('general')

  // API calls
  const { data: categoriesData } = useCategories({ page: 1, limit: 100, includeChildren: false })
  const { data: taxesData } = useTaxes()

  const categories = categoriesData?.categories || []
  const taxes = taxesData?.taxes || []

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      trackStock: false,
      unit: 'PIECE',
      available: true,
      sellable: true,
      hasVariants: false,
      hasModifiers: false,
      displayOrder: 0,
      active: true,
      ...initialData
    }
  })

  const trackStock = watch('trackStock')

  useEffect(() => {
    if (isOpen && initialData) {
      reset({
        trackStock: false,
        unit: 'PIECE',
        available: true,
        sellable: true,
        hasVariants: false,
        hasModifiers: false,
        displayOrder: 0,
        active: true,
        ...initialData
      })
    } else if (isOpen) {
      reset()
    }
  }, [isOpen, initialData, reset])

  const handleFormSubmit = (data: ProductFormData) => {
    onSubmit(data)
  }

  const handleClose = () => {
    reset()
    setActiveTab('general')
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-[#202325]">
            {mode === 'create' ? 'Yeni Ürün Ekle' : 'Ürün Düzenle'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">Genel</TabsTrigger>
              <TabsTrigger value="pricing">Fiyat</TabsTrigger>
              <TabsTrigger value="stock">Stok</TabsTrigger>
              <TabsTrigger value="settings">Ayarlar</TabsTrigger>
            </TabsList>

            {/* Genel Bilgiler */}
            <TabsContent value="general" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="categoryId">Kategori *</Label>
                  <select
                    {...register('categoryId')}
                    className="w-full h-10 px-3 rounded-md border border-gray-200 bg-white"
                  >
                    <option value="">Kategori seçin</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  {errors.categoryId && (
                    <p className="text-sm text-red-600 mt-1">{errors.categoryId.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="code">Ürün Kodu *</Label>
                  <Input
                    {...register('code')}
                    placeholder="Örn: BURGER001"
                    className="h-10"
                  />
                  {errors.code && (
                    <p className="text-sm text-red-600 mt-1">{errors.code.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="name">Ürün Adı *</Label>
                <Input
                  {...register('name')}
                  placeholder="Ürün adını giriniz"
                  className="h-10"
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Açıklama</Label>
                <textarea
                  {...register('description')}
                  placeholder="Ürün açıklaması..."
                  className="w-full h-20 px-3 py-2 rounded-md border border-gray-200 resize-none"
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="barcode">Barkod</Label>
                <Input
                  {...register('barcode')}
                  placeholder="Barkod numarası"
                  className="h-10"
                />
                {errors.barcode && (
                  <p className="text-sm text-red-600 mt-1">{errors.barcode.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="image">Resim URL</Label>
                <Input
                  {...register('image')}
                  placeholder="https://example.com/image.jpg"
                  className="h-10"
                />
                {errors.image && (
                  <p className="text-sm text-red-600 mt-1">{errors.image.message}</p>
                )}
              </div>
            </TabsContent>

            {/* Fiyatlandırma */}
            <TabsContent value="pricing" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="basePrice">Temel Fiyat *</Label>
                  <Input
                    {...register('basePrice')}
                    placeholder="0.00"
                    type="number"
                    step="0.01"
                    className="h-10"
                  />
                  {errors.basePrice && (
                    <p className="text-sm text-red-600 mt-1">{errors.basePrice.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="taxId">Vergi Oranı *</Label>
                  <select
                    {...register('taxId')}
                    className="w-full h-10 px-3 rounded-md border border-gray-200 bg-white"
                  >
                    <option value="">Vergi oranı seçin</option>
                    {taxes.map((tax) => (
                      <option key={tax.id} value={tax.id}>
                        {tax.name} (%{tax.rate})
                      </option>
                    ))}
                  </select>
                  {errors.taxId && (
                    <p className="text-sm text-red-600 mt-1">{errors.taxId.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Stok Yönetimi */}
            <TabsContent value="stock" className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  {...register('trackStock')}
                  className="h-4 w-4"
                />
                <Label>Stok takibi yap</Label>
              </div>

              {trackStock && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="unit">Birim</Label>
                    <select
                      {...register('unit')}
                      className="w-full h-10 px-3 rounded-md border border-gray-200 bg-white"
                    >
                      {unitOptions.map((unit) => (
                        <option key={unit.value} value={unit.value}>
                          {unit.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="criticalStock">Kritik Stok Seviyesi</Label>
                    <Input
                      {...register('criticalStock')}
                      placeholder="0.000"
                      type="number"
                      step="0.001"
                      className="h-10"
                    />
                    {errors.criticalStock && (
                      <p className="text-sm text-red-600 mt-1">{errors.criticalStock.message}</p>
                    )}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Ayarlar */}
            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('available')}
                    className="h-4 w-4"
                  />
                  <Label>Mevcut</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('sellable')}
                    className="h-4 w-4"
                  />
                  <Label>Satılabilir</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('hasVariants')}
                    className="h-4 w-4"
                  />
                  <Label>Varyantları var</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('hasModifiers')}
                    className="h-4 w-4"
                  />
                  <Label>Modifiyerleri var</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    {...register('active')}
                    className="h-4 w-4"
                  />
                  <Label>Aktif</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="preparationTime">Hazırlık Süresi (dakika)</Label>
                  <Input
                    {...register('preparationTime', { valueAsNumber: true })}
                    placeholder="0"
                    type="number"
                    min="0"
                    max="999"
                    className="h-10"
                  />
                  {errors.preparationTime && (
                    <p className="text-sm text-red-600 mt-1">{errors.preparationTime.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="displayOrder">Görüntüleme Sırası</Label>
                  <Input
                    {...register('displayOrder', { valueAsNumber: true })}
                    placeholder="0"
                    type="number"
                    min="0"
                    className="h-10"
                  />
                  {errors.displayOrder && (
                    <p className="text-sm text-red-600 mt-1">{errors.displayOrder.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="h-11 px-6"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="h-11 px-6 bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white"
            >
              {isLoading ? 'Kaydediliyor...' : mode === 'create' ? 'Ürün Ekle' : 'Güncelle'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
