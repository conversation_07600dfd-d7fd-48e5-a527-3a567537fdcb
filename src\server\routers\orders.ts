import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { router, protectedProcedure, paginationSchema, idSchema } from '../trpc'
import { OrderType, OrderStatus, PaymentStatus, OrderItemStatus } from '@prisma/client'

const createOrderSchema = z.object({
  orderType: z.nativeEnum(OrderType),
  tableId: z.string().cuid().optional(),
  customerCount: z.number().positive().optional(),
  customerId: z.string().cuid().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  deliveryAddress: z.string().optional(),
  orderNote: z.string().optional(),
  kitchenNote: z.string().optional(),
  items: z.array(z.object({
    productId: z.string().cuid(),
    variantId: z.string().cuid().optional(),
    quantity: z.number().positive(),
    note: z.string().optional(),
    modifiers: z.array(z.object({
      modifierId: z.string().cuid(),
      quantity: z.number().positive().default(1),
    })).optional(),
  })),
})

const updateOrderSchema = z.object({
  id: z.string().cuid(),
  status: z.nativeEnum(OrderStatus).optional(),
  customerCount: z.number().positive().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  deliveryAddress: z.string().optional(),
  orderNote: z.string().optional(),
  kitchenNote: z.string().optional(),
})

const addOrderItemSchema = z.object({
  orderId: z.string().cuid(),
  productId: z.string().cuid(),
  variantId: z.string().cuid().optional(),
  quantity: z.number().positive(),
  note: z.string().optional(),
  modifiers: z.array(z.object({
    modifierId: z.string().cuid(),
    quantity: z.number().positive().default(1),
  })).optional(),
})

const updateOrderItemSchema = z.object({
  id: z.string().cuid(),
  quantity: z.number().positive().optional(),
  status: z.nativeEnum(OrderItemStatus).optional(),
  note: z.string().optional(),
})

const orderFilterSchema = z.object({
  status: z.nativeEnum(OrderStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  orderType: z.nativeEnum(OrderType).optional(),
  tableId: z.string().cuid().optional(),
  waiterId: z.string().cuid().optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
})

export const ordersRouter = router({
  // Sipariş listesi
  list: protectedProcedure
    .input(paginationSchema.merge(orderFilterSchema))
    .query(async ({ input, ctx }) => {
      const { page, limit, status, paymentStatus, orderType, tableId, waiterId, dateFrom, dateTo } = input
      const skip = (page - 1) * limit

      const where = {
        branchId: ctx.user.branchId || undefined,
        ...(status && { status }),
        ...(paymentStatus && { paymentStatus }),
        ...(orderType && { orderType }),
        ...(tableId && { tableId }),
        ...(waiterId && { waiterId }),
        ...(dateFrom || dateTo) && {
          orderedAt: {
            ...(dateFrom && { gte: dateFrom }),
            ...(dateTo && { lte: dateTo }),
          },
        },
      }

      const [orders, total] = await Promise.all([
        ctx.prisma.order.findMany({
          where,
          include: {
            table: true,
            waiter: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
            items: {
              include: {
                product: true,
                variant: true,
                modifiers: {
                  include: {
                    modifier: true,
                  },
                },
              },
            },
            payments: {
              include: {
                paymentMethod: true,
              },
            },
          },
          orderBy: { orderedAt: 'desc' },
          skip,
          take: limit,
        }),
        ctx.prisma.order.count({ where }),
      ])

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }
    }),

  // Tek sipariş getir
  getById: protectedProcedure
    .input(idSchema)
    .query(async ({ input, ctx }) => {
      const order = await ctx.prisma.order.findFirst({
        where: {
          id: input.id,
          branchId: ctx.user.branchId || undefined,
        },
        include: {
          table: true,
          customer: true,
          waiter: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          items: {
            include: {
              product: true,
              variant: true,
              modifiers: {
                include: {
                  modifier: true,
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          payments: {
            include: {
              paymentMethod: true,
            },
            orderBy: { paidAt: 'desc' },
          },
          invoice: true,
          logs: {
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
        },
      })

      if (!order) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Sipariş bulunamadı',
        })
      }

      return order
    }),

  // Yeni sipariş oluştur
  create: protectedProcedure
    .input(createOrderSchema)
    .mutation(async ({ input, ctx }) => {
      const { items, ...orderData } = input

      if (!ctx.user.branchId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Şube bilgisi gerekli',
        })
      }

      // Sipariş numarası oluştur
      const today = new Date()
      const year = today.getFullYear()
      const orderCount = await ctx.prisma.order.count({
        where: {
          branchId: ctx.user.branchId,
          orderedAt: {
            gte: new Date(year, 0, 1),
            lt: new Date(year + 1, 0, 1),
          },
        },
      })
      const orderNumber = `${year}-${String(orderCount + 1).padStart(4, '0')}`

      // Sipariş kalemlerini hesapla
      let subtotal = new Decimal(0)
      let taxAmount = new Decimal(0)

      const orderItemsData = []

      for (const item of items) {
        // Ürün bilgilerini getir
        const product = await ctx.prisma.product.findFirst({
          where: {
            id: item.productId,
            companyId: ctx.user.companyId,
            active: true,
            available: true,
            sellable: true,
          },
          include: {
            tax: true,
            variants: true,
          },
        })

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: `Ürün bulunamadı: ${item.productId}`,
          })
        }

        // Varyant fiyatını kontrol et
        let unitPrice = product.basePrice
        if (item.variantId) {
          const variant = product.variants.find(v => v.id === item.variantId)
          if (!variant || !variant.active) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Ürün varyantı bulunamadı',
            })
          }
          unitPrice = variant.price
        }

        // Modifiyer fiyatlarını hesapla
        let modifierPrice = new Decimal(0)
        const modifiersData = []

        if (item.modifiers) {
          for (const mod of item.modifiers) {
            const modifier = await ctx.prisma.modifier.findFirst({
              where: {
                id: mod.modifierId,
                active: true,
              },
            })

            if (!modifier) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'Modifiyer bulunamadı',
              })
            }

            modifierPrice = modifierPrice.plus(modifier.price.mul(mod.quantity))
            modifiersData.push({
              modifierId: mod.modifierId,
              quantity: mod.quantity,
              price: modifier.price,
            })
          }
        }

        // Toplam fiyat hesapla
        const itemUnitPrice = unitPrice.plus(modifierPrice)
        const itemTotal = itemUnitPrice.mul(item.quantity)
        const itemTaxAmount = itemTotal.mul(product.tax.rate).div(100)

        subtotal = subtotal.plus(itemTotal)
        taxAmount = taxAmount.plus(itemTaxAmount)

        orderItemsData.push({
          productId: item.productId,
          variantId: item.variantId,
          quantity: new Decimal(item.quantity),
          unitPrice: itemUnitPrice,
          taxRate: product.tax.rate,
          taxAmount: itemTaxAmount,
          totalAmount: itemTotal,
          note: item.note,
          modifiers: modifiersData,
        })
      }

      const totalAmount = subtotal.plus(taxAmount)

      // Sipariş oluştur
      const order = await ctx.prisma.order.create({
        data: {
          ...orderData,
          branchId: ctx.user.branchId,
          orderNumber,
          waiterId: ctx.user.id,
          subtotal,
          taxAmount,
          totalAmount,
          items: {
            create: orderItemsData,
          },
        },
        include: {
          items: {
            include: {
              product: true,
              variant: true,
              modifiers: {
                include: {
                  modifier: true,
                },
              },
            },
          },
        },
      })

      return order
    }),

  // Sipariş güncelle
  update: protectedProcedure
    .input(updateOrderSchema)
    .mutation(async ({ input, ctx }) => {
      const { id, ...data } = input

      const order = await ctx.prisma.order.findFirst({
        where: {
          id,
          branchId: ctx.user.branchId || undefined,
        },
      })

      if (!order) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Sipariş bulunamadı',
        })
      }

      const updatedOrder = await ctx.prisma.order.update({
        where: { id },
        data,
      })

      return updatedOrder
    }),

  // Sipariş kalemi ekle
  addItem: protectedProcedure
    .input(addOrderItemSchema)
    .mutation(async ({ input, ctx }) => {
      const { orderId, modifiers, ...itemData } = input

      // Sipariş kontrolü
      const order = await ctx.prisma.order.findFirst({
        where: {
          id: orderId,
          branchId: ctx.user.branchId || undefined,
          status: { in: [OrderStatus.PENDING, OrderStatus.CONFIRMED] },
        },
      })

      if (!order) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Sipariş bulunamadı veya düzenlenemez durumda',
        })
      }

      // Ürün bilgilerini getir ve fiyat hesapla
      const product = await ctx.prisma.product.findFirst({
        where: {
          id: itemData.productId,
          companyId: ctx.user.companyId,
          active: true,
          available: true,
          sellable: true,
        },
        include: {
          tax: true,
          variants: true,
        },
      })

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Ürün bulunamadı',
        })
      }

      // Fiyat hesaplama (create metodundaki gibi)
      let unitPrice = product.basePrice
      if (itemData.variantId) {
        const variant = product.variants.find(v => v.id === itemData.variantId)
        if (!variant || !variant.active) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Ürün varyantı bulunamadı',
          })
        }
        unitPrice = variant.price
      }

      // Modifiyer hesaplama
      let modifierPrice = new Decimal(0)
      const modifiersData = []

      if (modifiers) {
        for (const mod of modifiers) {
          const modifier = await ctx.prisma.modifier.findFirst({
            where: {
              id: mod.modifierId,
              active: true,
            },
          })

          if (!modifier) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Modifiyer bulunamadı',
            })
          }

          modifierPrice = modifierPrice.plus(modifier.price.mul(mod.quantity))
          modifiersData.push({
            modifierId: mod.modifierId,
            quantity: mod.quantity,
            price: modifier.price,
          })
        }
      }

      const itemUnitPrice = unitPrice.plus(modifierPrice)
      const itemTotal = itemUnitPrice.mul(itemData.quantity)
      const itemTaxAmount = itemTotal.mul(product.tax.rate).div(100)

      // Sipariş kalemi oluştur
      const orderItem = await ctx.prisma.orderItem.create({
        data: {
          ...itemData,
          orderId,
          quantity: new Decimal(itemData.quantity),
          unitPrice: itemUnitPrice,
          taxRate: product.tax.rate,
          taxAmount: itemTaxAmount,
          totalAmount: itemTotal,
          modifiers: {
            create: modifiersData,
          },
        },
        include: {
          product: true,
          variant: true,
          modifiers: {
            include: {
              modifier: true,
            },
          },
        },
      })

      // Sipariş toplamlarını güncelle
      const newSubtotal = order.subtotal.plus(itemTotal)
      const newTaxAmount = order.taxAmount.plus(itemTaxAmount)
      const newTotalAmount = newSubtotal.plus(newTaxAmount)

      await ctx.prisma.order.update({
        where: { id: orderId },
        data: {
          subtotal: newSubtotal,
          taxAmount: newTaxAmount,
          totalAmount: newTotalAmount,
        },
      })

      return orderItem
    }),

  // Sipariş kalemi güncelle
  updateItem: protectedProcedure
    .input(updateOrderItemSchema)
    .mutation(async ({ input, ctx }) => {
      const { id, ...data } = input

      const orderItem = await ctx.prisma.orderItem.findFirst({
        where: {
          id,
          order: {
            branchId: ctx.user.branchId || undefined,
          },
        },
        include: {
          order: true,
        },
      })

      if (!orderItem) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Sipariş kalemi bulunamadı',
        })
      }

      // Miktar değişikliği varsa toplam hesapla
      if (data.quantity && data.quantity !== orderItem.quantity.toNumber()) {
        const newQuantity = new Decimal(data.quantity)
        const newTotal = orderItem.unitPrice.mul(newQuantity)
        const newTaxAmount = newTotal.mul(orderItem.taxRate).div(100)

        await ctx.prisma.orderItem.update({
          where: { id },
          data: {
            ...data,
            quantity: newQuantity,
            totalAmount: newTotal,
            taxAmount: newTaxAmount,
          },
        })

        // Sipariş toplamlarını yeniden hesapla
        const orderItems = await ctx.prisma.orderItem.findMany({
          where: { orderId: orderItem.orderId },
        })

        const subtotal = orderItems.reduce((sum, item) => sum.plus(item.totalAmount), new Decimal(0))
        const taxAmount = orderItems.reduce((sum, item) => sum.plus(item.taxAmount), new Decimal(0))

        await ctx.prisma.order.update({
          where: { id: orderItem.orderId },
          data: {
            subtotal,
            taxAmount,
            totalAmount: subtotal.plus(taxAmount),
          },
        })
      } else {
        await ctx.prisma.orderItem.update({
          where: { id },
          data,
        })
      }

      return { success: true }
    }),

  // Sipariş kalemi sil
  removeItem: protectedProcedure
    .input(idSchema)
    .mutation(async ({ input, ctx }) => {
      const orderItem = await ctx.prisma.orderItem.findFirst({
        where: {
          id: input.id,
          order: {
            branchId: ctx.user.branchId || undefined,
            status: { in: [OrderStatus.PENDING, OrderStatus.CONFIRMED] },
          },
        },
        include: {
          order: true,
        },
      })

      if (!orderItem) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Sipariş kalemi bulunamadı veya silinemez durumda',
        })
      }

      await ctx.prisma.orderItem.delete({
        where: { id: input.id },
      })

      // Sipariş toplamlarını güncelle
      const newSubtotal = orderItem.order.subtotal.minus(orderItem.totalAmount)
      const newTaxAmount = orderItem.order.taxAmount.minus(orderItem.taxAmount)

      await ctx.prisma.order.update({
        where: { id: orderItem.orderId },
        data: {
          subtotal: newSubtotal,
          taxAmount: newTaxAmount,
          totalAmount: newSubtotal.plus(newTaxAmount),
        },
      })

      return { success: true }
    }),
})

