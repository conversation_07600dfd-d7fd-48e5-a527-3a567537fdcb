import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { SearchIcon, EditIcon, PlusIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'
import { useCategories, useDeleteCategory } from '../hooks/useCategories'

/**
 * Kategori Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Hiyerarşik kategori yapısını gösterir
 * - Kategori kartları ile görsel sunum
 * - Arama ve filtreleme özelliği
 * - Alt kategori gösterimi
 * - Touch-friendly tasarım
 */

interface Category {
  id: string
  name: string
  description?: string | null
  color?: string | null
  icon?: string | null
  displayOrder: number
  active: boolean
  parentId?: string | null
  children?: Category[]
  _count?: {
    products: number
    children: number
  }
}

interface CategoryListProps {
  searchQuery?: string
  onCategoryEdit?: (categoryId: string) => void
  onCategoryDelete?: (categoryId: string) => void
  onNewCategory?: () => void
}

// Category interface - API'den gelen veri yapısı ile uyumlu
interface Category {
  id: string
  name: string
  description?: string | null
  color?: string | null
  icon?: string | null
  displayOrder: number
  active: boolean
  parentId?: string | null
  children?: Category[]
  _count?: {
    products: number
    children: number
  }
}

// Mock data kaldırıldı - artık gerçek API kullanılıyor

export function CategoryList({ searchQuery = '', onCategoryEdit, onCategoryDelete, onNewCategory }: CategoryListProps) {
  const [localSearch, setLocalSearch] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  // 300ms debounce for search
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)

  // API calls
  const { data: categoriesData, isLoading: isLoadingCategories, error: categoriesError } = useCategories({
    search: debouncedSearch || undefined,
    page: 1,
    limit: 50,
    includeChildren: true
  })

  const deleteCategoryMutation = useDeleteCategory()

  const categories = categoriesData?.categories || []

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const handleDeleteCategory = async (categoryId: string) => {
    if (window.confirm('Bu kategoriyi silmek istediğinizden emin misiniz? Alt kategoriler ve ürünler de etkilenebilir.')) {
      try {
        await deleteCategoryMutation.mutateAsync({ id: categoryId })
        onCategoryDelete?.(categoryId)
      } catch (error) {
        // Error handling is done in the hook
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Kategoriler</h2>
          <p className="text-sm text-[#636566]">{categories.length} kategori bulundu</p>
        </div>
        
        <Button 
          onClick={onNewCategory}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Kategori Ekle
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon className="w-5 h-5 text-gray-400" />
        </div>
        <Input
          type="text"
          placeholder="Kategori ara..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10 h-10 rounded-xl border-gray-200"
        />
      </div>

      {/* Categories */}
      {categories.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Kategori bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className="space-y-4">
          {categories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              isExpanded={expandedCategories.has(category.id)}
              onToggle={() => toggleCategory(category.id)}
              onEdit={() => onCategoryEdit?.(category.id)}
              onDelete={() => handleDeleteCategory(category.id)}
              onEditChild={(childId) => onCategoryEdit?.(childId)}
              onDeleteChild={(childId) => handleDeleteCategory(childId)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Category Card Component
interface CategoryCardProps {
  category: Category
  isExpanded: boolean
  onToggle: () => void
  onEdit: () => void
  onDelete: () => void
  onEditChild: (childId: string) => void
  onDeleteChild: (childId: string) => void
}

function CategoryCard({ 
  category, 
  isExpanded, 
  onToggle, 
  onEdit, 
  onDelete, 
  onEditChild, 
  onDeleteChild 
}: CategoryCardProps) {
  const hasChildren = category.children && category.children.length > 0

  return (
    <div className="space-y-2">
      {/* Main Category Card */}
      <Card className="overflow-hidden rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardContent className="p-5">
          <div className="flex items-center gap-4">
            {/* Category Icon/Color */}
            <div 
              className="w-12 h-12 rounded-xl flex items-center justify-center text-white text-xl font-bold flex-shrink-0"
              style={{ backgroundColor: category.color || '#025cca' }}
            >
              {category.icon || category.name.charAt(0)}
            </div>
            
            {/* Category Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-[#202325] text-lg">{category.name}</h3>
                {hasChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onToggle}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                  >
                    <span className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
                      ▶
                    </span>
                  </Button>
                )}
              </div>
              
              {category.description && (
                <p className="text-[#636566] text-sm mb-2">{category.description}</p>
              )}
              
              <div className="flex items-center gap-4 text-sm text-[#636566]">
                <span>{category._count?.products || 0} ürün</span>
                {hasChildren && (
                  <span>{category._count?.children || 0} alt kategori</span>
                )}
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 px-4"
              >
                <EditIcon className="w-4 h-4 mr-1" />
                Düzenle
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onDelete}
                className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3"
              >
                Sil
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sub Categories */}
      {hasChildren && isExpanded && (
        <div className="ml-8 space-y-2">
          {category.children!.map((child) => (
            <Card key={child.id} className="rounded-xl border border-gray-100 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  {/* Child Category Color */}
                  <div 
                    className="w-8 h-8 rounded-lg flex-shrink-0"
                    style={{ backgroundColor: child.color || category.color || '#025cca' }}
                  />
                  
                  {/* Child Info */}
                  <div className="flex-1">
                    <h4 className="font-semibold text-[#202325] text-base">{child.name}</h4>
                    <span className="text-sm text-[#636566]">{child._count?.products || 0} ürün</span>
                  </div>
                  
                  {/* Child Actions */}
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditChild(child.id)}
                      className="h-8 w-8 p-0 text-[#025cca] hover:bg-[#f0f8ff]"
                    >
                      <EditIcon className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteChild(child.id)}
                      className="h-8 w-8 p-0 text-[#ee4e4f] hover:bg-red-50"
                    >
                      ×
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
