import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type { 
  ProductVariantCreateInput,
  ProductVariantUpdateInput,
  ProductVariantOutput
} from '../../lib/validations/products'

/**
 * ProductVariantService - Ürün varyant yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Ürün varyant CRUD işlemleri
 * - Ürün hasVariants özelliği yönetimi
 * - Decimal.js ile güvenli para hesaplamaları
 */
export class ProductVariantService {
  /**
   * Ürün varyantı oluşturur
   * @param input - Varyant bilgileri
   * @returns Oluşturulan varyant ID'si
   */
  static async createProductVariant(input: ProductVariantCreateInput): Promise<string> {
    const { productId, name, code, price, displayOrder, active } = input

    // Ürün kontrolü
    const product = await prisma.product.findUnique({
      where: { id: productId },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Aynı kodda varyant kontrolü
    const existingVariant = await prisma.productVariant.findFirst({
      where: {
        productId,
        code,
      },
    })

    if (existingVariant) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu kodda bir varyant zaten mevcut',
      })
    }

    // Varyant oluştur
    const variant = await prisma.productVariant.create({
      data: {
        productId,
        name,
        code,
        price: new Decimal(price),
        displayOrder,
        active,
      },
    })

    // Ürünün hasVariants özelliğini güncelle
    await prisma.product.update({
      where: { id: productId },
      data: { hasVariants: true },
    })

    return variant.id
  }

  /**
   * Ürün varyantı günceller
   * @param input - Güncellenecek varyant bilgileri
   * @returns Başarı durumu
   */
  static async updateProductVariant(input: ProductVariantUpdateInput): Promise<boolean> {
    const { id, ...updateData } = input

    // Varyant varlık kontrolü
    const existingVariant = await prisma.productVariant.findUnique({
      where: { id },
    })

    if (!existingVariant) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Varyant bulunamadı',
      })
    }

    // Kod değişikliği kontrolü
    if (updateData.code && updateData.code !== existingVariant.code) {
      const duplicateVariant = await prisma.productVariant.findFirst({
        where: {
          productId: existingVariant.productId,
          code: updateData.code,
          id: { not: id },
        },
      })

      if (duplicateVariant) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu kodda bir varyant zaten mevcut',
        })
      }
    }

    // Decimal dönüşümü
    const processedUpdateData = {
      ...updateData,
      ...(updateData.price && { price: new Decimal(updateData.price) }),
    }

    // Varyant güncelle
    await prisma.productVariant.update({
      where: { id },
      data: processedUpdateData,
    })

    return true
  }

  /**
   * Ürün varyantı siler
   * @param id - Varyant ID'si
   * @returns Başarı durumu
   */
  static async deleteProductVariant(id: string): Promise<boolean> {
    // Varyant varlık kontrolü
    const variant = await prisma.productVariant.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orderItems: true,
          },
        },
      },
    })

    if (!variant) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Varyant bulunamadı',
      })
    }

    // Sipariş kontrolü
    if (variant._count.orderItems > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Siparişlerde kullanılan varyant silinemez',
      })
    }

    // Varyant sil
    await prisma.productVariant.delete({
      where: { id },
    })

    // Ürünün kalan varyant sayısını kontrol et
    const remainingVariants = await prisma.productVariant.count({
      where: { productId: variant.productId },
    })

    // Eğer varyant kalmadıysa hasVariants'ı false yap
    if (remainingVariants === 0) {
      await prisma.product.update({
        where: { id: variant.productId },
        data: { hasVariants: false },
      })
    }

    return true
  }

  /**
   * Ürünün varyantlarını getirir
   * @param productId - Ürün ID'si
   * @param activeOnly - Sadece aktif varyantlar
   * @returns Varyant listesi
   */
  static async getProductVariants(
    productId: string,
    activeOnly = true
  ): Promise<ProductVariantOutput[]> {
    const variants = await prisma.productVariant.findMany({
      where: {
        productId,
        ...(activeOnly && { active: true }),
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: { displayOrder: 'asc' },
    })

    return variants.map(variant => ({
      ...variant,
      price: variant.price.toString(),
    })) as ProductVariantOutput[]
  }

  /**
   * ID'ye göre varyant getirir
   * @param id - Varyant ID'si
   * @returns Varyant bilgisi
   */
  static async getProductVariantById(id: string): Promise<ProductVariantOutput> {
    const variant = await prisma.productVariant.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    if (!variant) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Varyant bulunamadı',
      })
    }

    return {
      ...variant,
      price: variant.price.toString(),
    } as ProductVariantOutput
  }

  /**
   * Varyant sırasını günceller
   * @param variantIds - Sıralanmış varyant ID'leri
   * @returns Başarı durumu
   */
  static async updateVariantOrder(variantIds: string[]): Promise<boolean> {
    // Transaction içinde sıra güncelleme
    await prisma.$transaction(
      variantIds.map((variantId, index) =>
        prisma.productVariant.update({
          where: { id: variantId },
          data: { displayOrder: index },
        })
      )
    )

    return true
  }

  /**
   * Ürünün varyant sayısını getirir
   * @param productId - Ürün ID'si
   * @returns Varyant sayısı
   */
  static async getVariantCount(productId: string): Promise<number> {
    return await prisma.productVariant.count({
      where: { productId },
    })
  }

  /**
   * Toplu varyant oluşturur
   * @param productId - Ürün ID'si
   * @param variants - Varyant listesi
   * @returns Oluşturulan varyant ID'leri
   */
  static async createBulkVariants(
    productId: string,
    variants: Omit<ProductVariantCreateInput, 'productId'>[]
  ): Promise<string[]> {
    // Ürün kontrolü
    const product = await prisma.product.findUnique({
      where: { id: productId },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Kod çakışması kontrolü
    const codes = variants.map(v => v.code)
    const existingVariants = await prisma.productVariant.findMany({
      where: {
        productId,
        code: { in: codes },
      },
    })

    if (existingVariants.length > 0) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: `Bu kodlarda varyantlar zaten mevcut: ${existingVariants.map(v => v.code).join(', ')}`,
      })
    }

    // Toplu varyant oluştur
    const createdVariants = await prisma.$transaction(
      variants.map(variant =>
        prisma.productVariant.create({
          data: {
            productId,
            name: variant.name,
            code: variant.code,
            price: new Decimal(variant.price),
            displayOrder: variant.displayOrder || 0,
            active: variant.active !== false,
          },
        })
      )
    )

    // Ürünün hasVariants özelliğini güncelle
    await prisma.product.update({
      where: { id: productId },
      data: { hasVariants: true },
    })

    return createdVariants.map(v => v.id)
  }
}
