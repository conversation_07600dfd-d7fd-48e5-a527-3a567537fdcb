import { router } from '../trpc'
import { authRouter } from './auth'
import { usersRouter } from './users'
import { productsRouter } from './products'
import { ordersRouter } from './orders'
import { dashboardRouter } from './dashboard'

export const appRouter = router({
  auth: authRouter,
  users: usersRouter,
  products: productsRouter,
  orders: ordersRouter,
  dashboard: dashboardRouter,
})

export type AppRouter = typeof appRouter

