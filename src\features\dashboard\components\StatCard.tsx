import { TrendingUp, TrendingDown } from "lucide-react"

interface StatCardProps {
  title: string
  value: string
  change: string
  isPositive: boolean
  icon: React.ReactNode
  iconBgColor: string
}

/**
 * İstatistik kartı bileşeni
 * 
 * Bu bileşen:
 * - <PERSON><PERSON><PERSON><PERSON><PERSON>, değer ve değişim yüzdesini gösterir
 * - Pozitif/negatif değişimleri farklı renklerle gösterir
 * - Touch-friendly tasarım (44px minimum)
 * - Responsive layout
 */
export function StatCard({
  title,
  value,
  change,
  isPositive,
  icon,
  iconBgColor,
}: StatCardProps) {
  return (
    <div className="bg-white rounded-xl p-5 border border-gray-100 w-full">
      <div className="flex justify-between items-start mb-3">
        <span className="text-[16px] font-medium text-[#202325]">
          {title}
        </span>
        <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${iconBgColor}`}>
          {icon}
        </div>
      </div>

      <div className="text-[36px] font-semibold text-[#202325] mb-2 leading-none">
        {value}
      </div>

      <div className="text-[12px]">
        <span className={`font-semibold ${isPositive ? "text-[#40AC76]" : "text-[#EE4E4F]"}`}>
          {isPositive ? '+' : ''}{change}
        </span>
        <span className="text-[#636566]"> dünden</span>
      </div>
    </div>
  )
}
