import { Search, Check, Clock, ArrowRight, Plus } from "lucide-react"
import { useOrdersList } from "../hooks/useOrdersList"
import { Skeleton } from "../../../components/ui/skeleton"
import { toast } from "sonner"
import { useEffect } from "react"
import type { OrderListItem } from "../types"

/**
 * Sipariş paneli bileşeni
 * 
 * Bu bileşen:
 * - "In Progress" ve "Waiting for Payment" sekmeleri arasında geçiş yapar
 * - Sipariş arama kutusu sağlar (300ms debounce)
 * - Dinamik sipariş listesi gösterir
 * - Boş durum ("You Don't Have Any Orders") handle eder
 * - Sipariş durumlarını görsel olarak yansıtır
 * - "Create New Order" butonu sağlar
 */
export function OrderPanel() {
  const { 
    filteredOrders, 
    isLoading, 
    error, 
    searchQuery, 
    setSearchQuery, 
    status, 
    setStatus 
  } = useOrdersList()

  // Hata durumunda toast göster
  useEffect(() => {
    if (error) {
      toast.error('Siparişler yüklenirken hata oluştu')
      console.error('Orders list error:', error)
    }
  }, [error])

  const getStatusBadge = (order: OrderListItem) => {
    switch (order.statusColor) {
      case "green":
        return (
          <div className="flex flex-col items-end gap-1">
            <div className="flex items-center gap-1 px-2 py-1 bg-[#DCF7EA] rounded-lg">
              <Check className="w-4 h-4 text-[#286B4A]" />
              <span className="text-[12px] font-medium text-[#286B4A]">
                Ready
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-[#48C185] rounded-full"></div>
              <span className="text-[12px] font-medium text-[#636566]">
                {order.status}
              </span>
            </div>
          </div>
        )
      case "orange":
      case "red":
        return (
          <div className="flex flex-col items-end gap-1">
            <div className="flex items-center gap-1 px-2 py-1 bg-[#F5F5F5] rounded-lg">
              <Clock className="w-4 h-4 text-[#202325]" />
              <span className="text-[12px] font-medium text-[#202325]">
                In Progress
              </span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-[#202325] rounded-full"></div>
              <span className="text-[12px] font-medium text-[#636566]">
                {order.status}
              </span>
            </div>
          </div>
        )
      case "blue":
        return order.hasPayButton ? (
          <button className="flex items-center gap-1 px-3 py-2 bg-[#025CCA] text-white rounded-lg hover:bg-blue-700 transition-colors min-h-[44px]">
            <span className="text-[12px] font-medium">Pay Now</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        ) : (
          <div className="flex items-center gap-1 px-2 py-1 bg-[#DCEEFE] rounded-lg">
            <span className="text-[12px] font-medium text-[#025CCA]">
              {order.status}
            </span>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="bg-white rounded-2xl p-5 border border-gray-100 h-[719px]">
      {/* Header with Tabs */}
      <div className="flex h-[52px] bg-[#F5F5F5] rounded-xl mb-6">
        <button
          className={`flex-1 flex items-center justify-center py-2 px-3 rounded-xl transition-colors min-h-[44px] ${
            status === "inProgress"
              ? "bg-[#DCEEFE] text-[#025CCA] font-semibold shadow-sm"
              : "text-[#636566] font-medium"
          }`}
          onClick={() => setStatus("inProgress")}
        >
          Devam Eden
        </button>
        <button
          className={`flex-1 flex items-center justify-center py-2 px-3 rounded-xl transition-colors min-h-[44px] ${
            status === "waitingForPayment"
              ? "bg-[#DCEEFE] text-[#025CCA] font-semibold shadow-sm"
              : "text-[#636566] font-medium"
          }`}
          onClick={() => setStatus("waitingForPayment")}
        >
          Ödeme Bekliyor
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-[#202325]" />
          <input
            type="text"
            placeholder="Sipariş Ara"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-4 py-3 bg-[#F5F5F5] rounded-xl text-[14px] placeholder-[#797B7C] focus:outline-none focus:ring-2 focus:ring-blue-500 border-0 min-h-[44px]"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          // Loading skeleton
          <div className="space-y-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="w-[50px] h-[50px] rounded-xl" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <Skeleton className="h-8 w-20 rounded-lg" />
              </div>
            ))}
          </div>
        ) : !filteredOrders || filteredOrders.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-[400px]">
            <div className="bg-[#f0f8ff] rounded-full w-[126px] h-[126px] flex items-center justify-center mb-8">
              <div className="w-11 h-11 flex items-center justify-center">
                <Plus className="w-8 h-8 text-[#025CCA]" />
              </div>
            </div>
            <div className="text-center mb-6">
              <h3 className="text-[16px] font-semibold text-[#202325] mb-2">
                Hiç Siparişiniz Yok
              </h3>
              <p className="text-[14px] text-[#636566] w-[266px]">
                Bugün müşterilerinizden henüz sipariş almadınız.
              </p>
            </div>
            <button className="flex items-center gap-2 px-4 py-2 bg-[#025CCA] text-white rounded-lg hover:bg-blue-700 transition-colors min-h-[44px]">
              <span className="text-[12px] font-semibold">Yeni Sipariş Oluştur</span>
            </button>
          </div>
        ) : (
          // Orders list
          <div className="space-y-6 overflow-y-auto max-h-[500px]">
            {filteredOrders.map((order) => (
              <div key={order.orderId} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-[50px] h-[50px] bg-[#F0F8FF] rounded-xl flex items-center justify-center">
                    <span className="text-[16px] font-semibold text-[#357DD5]">
                      {order.orderNumber}
                    </span>
                  </div>
                  <div>
                    <div className="text-[16px] font-medium text-[#202325]">
                      {order.customerName || 'Müşteri'}
                    </div>
                    <div className="text-[14px] text-[#636566]">
                      {order.totalItems} Ürün
                    </div>
                  </div>
                </div>
                {getStatusBadge(order)}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
