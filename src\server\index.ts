import express from 'express'
import cors from 'cors'
import { createExpressMiddleware } from '@trpc/server/adapters/express'
import { appRouter } from './routers'
import { prisma } from './db'
import type { Context } from './trpc'

const app = express()
const PORT = process.env.SERVER_PORT || 3001

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'], // React dev server URLs
  credentials: true,
}))

app.use(express.json())

// Context oluşturma fonksiyonu
const createContext = async ({ req }: { req: express.Request }): Promise<Context> => {
  // Authorization header'dan token'ı al
  const token = req.headers.authorization?.replace('Bearer ', '')
  
  let user = undefined
  
  if (token) {
    try {
      // Token'ı doğrula ve kullanıcı bilgilerini al
      const session = await prisma.session.findUnique({
        where: { token },
        include: {
          user: true,
        },
      })
      
      if (session && !session.endedAt) {
        // Session'ın son aktivite zamanın<PERSON> güncelle
        await prisma.session.update({
          where: { id: session.id },
          data: { lastActivityAt: new Date() },
        })
        
        user = {
          id: session.user.id,
          companyId: session.user.companyId,
          branchId: session.user.branchId || undefined,
          role: session.user.role,
        }
      }
    } catch (error) {
      console.error('Token validation error:', error)
      // Token geçersizse user undefined kalır
    }
  }
  
  return {
    prisma,
    user,
  }
}

// tRPC middleware
app.use(
  '/trpc',
  createExpressMiddleware({
    router: appRouter,
    createContext,
    onError: ({ error, path, input }) => {
      console.error(`❌ tRPC Error on ${path}:`, error)
      console.error('Input:', input)
    },
  })
)

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// Root endpoint
app.get('/', (_req, res) => {
  res.json({ 
    message: 'ATROPOS Restaurant POS API Server',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      trpc: '/trpc',
    }
  })
})

// Error handling middleware
app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Express Error:', err)
  res.status(500).json({ 
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  })
})

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n🛑 Shutting down server...')
  await prisma.$disconnect()
  process.exit(0)
})

// Server'ı başlat
app.listen(PORT, () => {
  console.log(`🚀 ATROPOS API Server running on http://localhost:${PORT}`)
  console.log(`📊 Health check: http://localhost:${PORT}/health`)
  console.log(`🔌 tRPC endpoint: http://localhost:${PORT}/trpc`)
  console.log(`📝 Environment: ${process.env.NODE_ENV || 'development'}`)
})

export default app
