import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type { 
  CategoryCreateInput, 
  CategoryUpdateInput, 
  CategoryListInput,
  CategoryOutput,
  CategoryListOutput 
} from '../../lib/validations/products'

/**
 * CategoryService - Kategori yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Hiyerarşik kategori yapısı yönetimi
 * - Kategori CRUD işlemleri
 * - Kategori doğrulama ve iş mantığı
 * - Kategori sıralama ve filtreleme
 */
export class CategoryService {
  /**
   * Kategori listesini getirir
   * @param input - Filtreleme ve sayfalama parametreleri
   * @param companyId - Şirket ID'si
   * @returns Kategori listesi
   */
  static async getCategories(
    input: CategoryListInput,
    companyId: string
  ): Promise<CategoryListOutput> {
    const { 
      parentId, 
      active, 
      includeChildren = false,
      page = 1, 
      limit = 10 
    } = input

    const skip = (page - 1) * limit

    // Where koşulları
    const where = {
      companyId,
      ...(parentId !== undefined && { parentId }),
      ...(active !== undefined && { active }),
    }

    // Include koşulları
    const include = {
      _count: {
        select: {
          products: true,
          children: true,
        },
      },
      ...(includeChildren && {
        children: {
          where: { active: true },
          orderBy: { displayOrder: 'asc' as const },
          include: {
            _count: {
              select: {
                products: true,
                children: true,
              },
            },
          },
        },
      }),
    }

    // Kategorileri getir
    const [categories, total] = await Promise.all([
      prisma.category.findMany({
        where,
        include,
        orderBy: { displayOrder: 'asc' },
        skip: limit ? skip : undefined,
        take: limit || undefined,
      }),
      prisma.category.count({ where }),
    ])

    const totalPages = limit ? Math.ceil(total / limit) : 1

    return {
      categories: categories as CategoryOutput[],
      total,
      page,
      limit: limit || total,
      totalPages,
    }
  }

  /**
   * ID'ye göre kategori getirir
   * @param id - Kategori ID'si
   * @param companyId - Şirket ID'si
   * @param includeChildren - Alt kategorileri dahil et
   * @returns Kategori bilgisi
   */
  static async getCategoryById(
    id: string,
    companyId: string,
    includeChildren = false
  ): Promise<CategoryOutput> {
    const category = await prisma.category.findFirst({
      where: { id, companyId },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
        ...(includeChildren && {
          children: {
            where: { active: true },
            orderBy: { displayOrder: 'asc' },
            include: {
              _count: {
                select: {
                  products: true,
                  children: true,
                },
              },
            },
          },
        }),
      },
    })

    if (!category) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kategori bulunamadı',
      })
    }

    return category as CategoryOutput
  }

  /**
   * Yeni kategori oluşturur
   * @param input - Kategori bilgileri
   * @param companyId - Şirket ID'si
   * @returns Oluşturulan kategori ID'si
   */
  static async createCategory(
    input: CategoryCreateInput,
    companyId: string
  ): Promise<string> {
    const {
      name,
      description,
      image,
      color,
      icon,
      displayOrder = 0,
      active = true,
      parentId,
      printerGroupId,
      preparationTime,
    } = input

    // Aynı isimde kategori kontrolü
    const existingCategory = await prisma.category.findFirst({
      where: {
        companyId,
        name,
        parentId,
      },
    })

    if (existingCategory) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu isimde bir kategori zaten mevcut',
      })
    }

    // Parent kategori kontrolü
    if (parentId) {
      const parentCategory = await prisma.category.findFirst({
        where: { id: parentId, companyId },
      })

      if (!parentCategory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Üst kategori bulunamadı',
        })
      }
    }

    // Printer group kontrolü
    if (printerGroupId) {
      const printerGroup = await prisma.printerGroup.findUnique({
        where: { id: printerGroupId },
      })

      if (!printerGroup) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Yazıcı grubu bulunamadı',
        })
      }
    }

    // Kategori oluştur
    const category = await prisma.category.create({
      data: {
        companyId,
        name,
        description,
        image,
        color,
        icon,
        displayOrder,
        active,
        parentId,
        printerGroupId,
        preparationTime,
      },
    })

    return category.id
  }

  /**
   * Kategori günceller
   * @param input - Güncellenecek kategori bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async updateCategory(
    input: CategoryUpdateInput,
    companyId: string
  ): Promise<boolean> {
    const { id, ...updateData } = input

    // Kategori varlık kontrolü
    const existingCategory = await prisma.category.findFirst({
      where: { id, companyId },
    })

    if (!existingCategory) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kategori bulunamadı',
      })
    }

    // İsim değişikliği kontrolü
    if (updateData.name && updateData.name !== existingCategory.name) {
      const duplicateCategory = await prisma.category.findFirst({
        where: {
          companyId,
          name: updateData.name,
          parentId: updateData.parentId ?? existingCategory.parentId,
          id: { not: id },
        },
      })

      if (duplicateCategory) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu isimde bir kategori zaten mevcut',
        })
      }
    }

    // Parent kategori kontrolü
    if (updateData.parentId) {
      // Kendi kendisini parent yapma kontrolü
      if (updateData.parentId === id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Kategori kendi kendisinin alt kategorisi olamaz',
        })
      }

      // Döngüsel referans kontrolü
      const isCircular = await this.checkCircularReference(id, updateData.parentId, companyId)
      if (isCircular) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Döngüsel kategori referansı oluşturulamaz',
        })
      }

      const parentCategory = await prisma.category.findFirst({
        where: { id: updateData.parentId, companyId },
      })

      if (!parentCategory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Üst kategori bulunamadı',
        })
      }
    }

    // Printer group kontrolü
    if (updateData.printerGroupId) {
      const printerGroup = await prisma.printerGroup.findUnique({
        where: { id: updateData.printerGroupId },
      })

      if (!printerGroup) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Yazıcı grubu bulunamadı',
        })
      }
    }

    // Kategori güncelle
    await prisma.category.update({
      where: { id },
      data: updateData,
    })

    return true
  }

  /**
   * Kategori siler
   * @param id - Kategori ID'si
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async deleteCategory(id: string, companyId: string): Promise<boolean> {
    // Kategori varlık kontrolü
    const category = await prisma.category.findFirst({
      where: { id, companyId },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    })

    if (!category) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Kategori bulunamadı',
      })
    }

    // Alt kategori kontrolü
    if (category._count.children > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Alt kategorileri olan kategori silinemez',
      })
    }

    // Ürün kontrolü
    if (category._count.products > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Ürünleri olan kategori silinemez',
      })
    }

    // Kategori sil
    await prisma.category.delete({
      where: { id },
    })

    return true
  }

  /**
   * Döngüsel referans kontrolü yapar
   * @param categoryId - Kontrol edilecek kategori ID'si
   * @param parentId - Potansiyel parent ID'si
   * @param companyId - Şirket ID'si
   * @returns Döngüsel referans var mı?
   */
  private static async checkCircularReference(
    categoryId: string,
    parentId: string,
    companyId: string
  ): Promise<boolean> {
    let currentParentId = parentId

    while (currentParentId) {
      if (currentParentId === categoryId) {
        return true
      }

      const parentCategory = await prisma.category.findFirst({
        where: { id: currentParentId, companyId },
        select: { parentId: true },
      })

      if (!parentCategory) {
        break
      }

      currentParentId = parentCategory.parentId
    }

    return false
  }

  /**
   * Kategori hiyerarşisini getirir
   * @param companyId - Şirket ID'si
   * @returns Hiyerarşik kategori ağacı
   */
  static async getCategoryHierarchy(companyId: string): Promise<CategoryOutput[]> {
    const categories = await prisma.category.findMany({
      where: {
        companyId,
        active: true,
      },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
      orderBy: { displayOrder: 'asc' },
    })

    // Hiyerarşik yapı oluştur
    const categoryMap = new Map<string, CategoryOutput>()
    const rootCategories: CategoryOutput[] = []

    // Tüm kategorileri map'e ekle
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] } as CategoryOutput)
    })

    // Hiyerarşik yapıyı oluştur
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId)
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.push(categoryWithChildren)
        }
      } else {
        rootCategories.push(categoryWithChildren)
      }
    })

    return rootCategories
  }
}
