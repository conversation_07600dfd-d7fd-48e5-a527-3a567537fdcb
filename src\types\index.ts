import type { 
  User, 
  Company, 
  Branch, 
  Product, 
  Category, 
  Order, 
  OrderItem, 
  Table, 
  Customer,
  PaymentMethod,
  Tax,
  UserRole,
  OrderStatus,
  PaymentStatus,
  OrderType,
  TableStatus,
  ProductUnit,
  PaymentMethodType
} from '@prisma/client'

// Genişletilmiş tipler
export interface UserWithRelations extends User {
  company: Company
  branch?: Branch
}

export interface ProductWithRelations extends Product {
  category: Category
  tax: Tax
  variants?: ProductVariant[]
  modifierGroups?: ProductModifierGroupWithRelations[]
}

export interface ProductVariant {
  id: string
  productId: string
  name: string
  code: string
  price: number
  displayOrder: number
  active: boolean
}

export interface ProductModifierGroupWithRelations {
  productId: string
  modifierGroupId: string
  displayOrder: number
  modifierGroup: ModifierGroupWithModifiers
}

export interface ModifierGroupWithModifiers {
  id: string
  name: string
  minSelection: number
  maxSelection: number
  required: boolean
  displayOrder: number
  active: boolean
  modifiers: Modifier[]
}

export interface Modifier {
  id: string
  groupId: string
  name: string
  price: number
  displayOrder: number
  active: boolean
}

export interface OrderWithRelations extends Order {
  table?: Table
  customer?: Customer
  waiter?: Pick<User, 'id' | 'firstName' | 'lastName'>
  items: OrderItemWithRelations[]
  payments?: PaymentWithRelations[]
}

export interface OrderItemWithRelations extends OrderItem {
  product: Product
  variant?: ProductVariant
  modifiers: OrderItemModifierWithRelations[]
}

export interface OrderItemModifierWithRelations {
  id: string
  orderItemId: string
  modifierId: string
  quantity: number
  price: number
  modifier: Modifier
}

export interface PaymentWithRelations {
  id: string
  orderId: string
  paymentMethodId: string
  amount: number
  tipAmount: number
  approvalCode?: string
  maskedCardNumber?: string
  status: PaymentStatus
  paidAt: Date
  refundedAt?: Date
  paymentMethod: PaymentMethod
}

// Form tipleri
export interface LoginForm {
  username: string
  password: string
}

export interface PinLoginForm {
  pin: string
}

export interface CreateOrderForm {
  orderType: OrderType
  tableId?: string
  customerCount?: number
  customerId?: string
  customerName?: string
  customerPhone?: string
  deliveryAddress?: string
  orderNote?: string
  kitchenNote?: string
  items: OrderItemForm[]
}

export interface OrderItemForm {
  productId: string
  variantId?: string
  quantity: number
  note?: string
  modifiers?: OrderItemModifierForm[]
}

export interface OrderItemModifierForm {
  modifierId: string
  quantity: number
}

export interface CreateProductForm {
  categoryId: string
  code: string
  barcode?: string
  name: string
  description?: string
  image?: string
  basePrice: number
  taxId: string
  trackStock: boolean
  unit: ProductUnit
  criticalStock?: number
  available: boolean
  sellable: boolean
  preparationTime?: number
  hasVariants: boolean
  hasModifiers: boolean
  displayOrder: number
}

// API Response tipleri
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface AuthResponse {
  user: UserWithRelations
  token?: string
}

// Store tipleri
export interface AuthStore {
  user: UserWithRelations | null
  token: string | null
  isAuthenticated: boolean
  login: (credentials: LoginForm) => Promise<void>
  pinLogin: (pin: string) => Promise<void>
  logout: () => void
  setUser: (user: UserWithRelations) => void
}

export interface CartStore {
  items: CartItem[]
  orderType: OrderType
  tableId?: string
  customerInfo?: CustomerInfo
  addItem: (item: CartItem) => void
  removeItem: (itemId: string) => void
  updateItem: (itemId: string, updates: Partial<CartItem>) => void
  clearCart: () => void
  setOrderType: (type: OrderType) => void
  setTable: (tableId: string) => void
  setCustomerInfo: (info: CustomerInfo) => void
  getTotal: () => number
  getSubtotal: () => number
  getTaxAmount: () => number
}

export interface CartItem {
  id: string
  productId: string
  variantId?: string
  name: string
  price: number
  quantity: number
  note?: string
  modifiers: CartItemModifier[]
  taxRate: number
}

export interface CartItemModifier {
  id: string
  name: string
  price: number
  quantity: number
}

export interface CustomerInfo {
  name?: string
  phone?: string
  address?: string
}

// Utility tipleri
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// Event tipleri
export interface OrderEvent {
  type: 'ORDER_CREATED' | 'ORDER_UPDATED' | 'ORDER_CANCELLED' | 'PAYMENT_RECEIVED'
  orderId: string
  data?: any
}

export interface TableEvent {
  type: 'TABLE_OCCUPIED' | 'TABLE_FREED' | 'TABLE_RESERVED'
  tableId: string
  data?: any
}

// Electron API tipleri
export interface ElectronAPI {
  store: {
    get: (key: string) => Promise<any>
    set: (key: string, value: any) => Promise<void>
    delete: (key: string) => Promise<void>
  }
  app: {
    getVersion: () => Promise<string>
    quit: () => Promise<void>
  }
}

// Global window interface
declare global {
  interface Window {
    electronAPI?: ElectronAPI
  }
}

// Enum re-exports
export {
  UserRole,
  OrderStatus,
  PaymentStatus,
  OrderType,
  TableStatus,
  ProductUnit,
  PaymentMethodType,
}

