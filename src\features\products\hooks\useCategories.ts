import React from 'react'
import { trpc } from '../../../lib/trpc-client'
import { toast } from 'sonner'
import type {
  CategoryListInput,
  CategoryCreateInput,
  CategoryUpdateInput,
} from '../../../lib/validations/products'

/**
 * Kategori yönetimi için TanStack Query hooks
 * 
 * Bu hooks:
 * - Kategori listesi getirme (hiyerarşik yapı)
 * - Kategori detayı getirme
 * - <PERSON><PERSON><PERSON> o<PERSON>şturma, güncelleme, silme
 * - Kategori hiyerarşisi getirme
 * - Error handling ve toast bildirimleri
 * - Cache invalidation
 */

/**
 * Kategori listesi getiren hook
 */
export function useCategories(input?: CategoryListInput) {
  return trpc.products.category.list.useQuery(
    input || { page: 1, limit: 50 },
    {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error) => {
        console.error('Kategori listesi getirme hatası:', error)
        toast.error('Kategori listesi yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Kategori hiyerarşisi getiren hook
 */
export function useCategoryHierarchy() {
  return trpc.products.category.getHierarchy.useQuery(
    undefined,
    {
      staleTime: 10 * 60 * 1000, // 10 dakika
      retry: 3,
      onError: (error) => {
        console.error('Kategori hiyerarşisi getirme hatası:', error)
        toast.error('Kategori hiyerarşisi yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Tek kategori detayı getiren hook
 */
export function useCategory(categoryId: string) {
  return trpc.products.category.getById.useQuery(
    { id: categoryId },
    {
      enabled: !!categoryId,
      staleTime: 5 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Kategori detayı getirme hatası:', error)
        toast.error('Kategori detayları yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Kategori oluşturma hook'u
 */
export function useCreateCategory() {
  const utils = trpc.useUtils()

  return trpc.products.category.create.useMutation({
    onSuccess: (data) => {
      toast.success('Kategori başarıyla oluşturuldu')
      
      // Cache'leri invalidate et
      utils.products.category.list.invalidate()
      utils.products.category.getHierarchy.invalidate()
    },
    onError: (error) => {
      console.error('Kategori oluşturma hatası:', error)
      
      const errorMessage = error.message || 'Kategori oluşturulurken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Kategori güncelleme hook'u
 */
export function useUpdateCategory() {
  const utils = trpc.useUtils()

  return trpc.products.category.update.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Kategori başarıyla güncellendi')
      
      // İlgili cache'leri invalidate et
      utils.products.category.list.invalidate()
      utils.products.category.getHierarchy.invalidate()
      utils.products.category.getById.invalidate({ id: variables.id })
    },
    onError: (error) => {
      console.error('Kategori güncelleme hatası:', error)
      
      const errorMessage = error.message || 'Kategori güncellenirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Kategori silme hook'u
 */
export function useDeleteCategory() {
  const utils = trpc.useUtils()

  return trpc.products.category.delete.useMutation({
    onSuccess: (data, variables) => {
      toast.success('Kategori başarıyla silindi')
      
      // Cache'leri invalidate et
      utils.products.category.list.invalidate()
      utils.products.category.getHierarchy.invalidate()
      
      // Silinen kategorinin cache'ini temizle
      utils.products.category.getById.removeQueries({ id: variables.id })
      
      // İlgili ürün listelerini de invalidate et
      utils.products.product.list.invalidate()
    },
    onError: (error) => {
      console.error('Kategori silme hatası:', error)
      
      const errorMessage = error.message || 'Kategori silinirken bir hata oluştu'
      toast.error(errorMessage)
    }
  })
}

/**
 * Kategori arama hook'u (debounced)
 */
export function useCategorySearch(searchQuery: string, delay: number = 300) {
  const [debouncedQuery, setDebouncedQuery] = React.useState(searchQuery)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
    }, delay)

    return () => clearTimeout(timer)
  }, [searchQuery, delay])

  return useCategories({
    search: debouncedQuery || undefined,
    page: 1,
    limit: 50
  })
}

/**
 * Ana kategoriler (parent'ı olmayan) hook'u
 */
export function useRootCategories() {
  return trpc.products.category.list.useQuery(
    {
      parentId: null, // Ana kategoriler
      page: 1,
      limit: 50
    },
    {
      staleTime: 5 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Ana kategori listesi getirme hatası:', error)
        toast.error('Ana kategori listesi yüklenirken bir hata oluştu')
      }
    }
  )
}

/**
 * Alt kategoriler hook'u
 */
export function useSubCategories(parentId: string) {
  return trpc.products.category.list.useQuery(
    {
      parentId,
      page: 1,
      limit: 50
    },
    {
      enabled: !!parentId,
      staleTime: 5 * 60 * 1000,
      retry: 3,
      onError: (error) => {
        console.error('Alt kategori listesi getirme hatası:', error)
        toast.error('Alt kategori listesi yüklenirken bir hata oluştu')
      }
    }
  )
}
