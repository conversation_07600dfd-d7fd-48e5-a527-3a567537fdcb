import { trpc } from '../../../lib/trpc-client'
import type { OverviewStatsInput } from '../types'

/**
 * Dashboard özet istatistiklerini getiren hook
 * 
 * Bu hook:
 * - Günlük toplam kazancı getirir
 * - <PERSON><PERSON> eden sipariş sayısını getirir  
 * - Bekleme listesi sayısını getirir
 * - Önceki güne göre yüzde değişimleri hesaplar
 */
export function useOverviewStats(input?: OverviewStatsInput) {
  return trpc.dashboard.getOverviewStats.useQuery(
    input || {},
    {
      staleTime: 2 * 60 * 1000, // 2 dakika
      refetchInterval: 30 * 1000, // 30 saniyede bir otomatik güncelle
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    }
  )
}
