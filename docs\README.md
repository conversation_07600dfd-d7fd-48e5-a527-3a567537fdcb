# Restaurant POS System

Modern, hızlı ve güvenilir restoran POS sistemi. Electron, React, TypeScript, tRPC, Prisma ve PostgreSQL teknolojileri ile geliştirilmiştir.

## 🚀 Özellikler

### Temel Özellikler
- **Modern Teknoloji Stack**: Electron + React + TypeScript
- **Güçlü Backend**: tRPC + Prisma + PostgreSQL
- **Responsive UI**: Tailwind CSS + shadcn/ui
- **State Management**: Zustand
- **Data Fetching**: TanStack Query
- **Feature-Based Architecture**: Modüler ve ölçeklenebilir yapı

### İş Özellikleri
- Kullanıcı yönetimi ve rol tabanlı yetkilendirme
- Ürün ve kategori yönetimi
- Sipariş alma ve takip sistemi
- Masa yönetimi
- Ödeme işlemleri
- Mutfak ekranı
- Raporlama sistemi
- Offline çalışabilme
- Çoklu şube desteği

## 📋 Gereksinimler

### Sistem Gereksinimleri
- **İşletim Sistemi**: Windows 10+, macOS 10.14+, Ubuntu 20.04+
- **RAM**: Minimum 4GB, Önerilen 8GB
- **Depolama**: Minimum 2GB boş alan
- **İnternet**: Senkronizasyon için gerekli, offline çalışabilir

### Geliştirme Gereksinimleri
- Node.js 20.x veya üzeri
- PostgreSQL 14.x veya üzeri
- Git

## 🛠️ Kurulum

### 1. Projeyi Klonlayın
```bash
git clone <repository-url>
cd restaurant-pos
```

### 2. Bağımlılıkları Yükleyin
```bash
npm install
```

### 3. Veritabanını Ayarlayın

#### PostgreSQL Kurulumu
PostgreSQL'i sisteminize kurun:

**Windows:**
1. [PostgreSQL resmi sitesinden](https://www.postgresql.org/download/windows/) indirin
2. Kurulum sihirbazını takip edin
3. Varsayılan port: 5432

**macOS:**
```bash
# Homebrew ile
brew install postgresql
brew services start postgresql
```

**Ubuntu:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Veritabanı Oluşturma
```bash
# PostgreSQL'e bağlanın
sudo -u postgres psql

# Veritabanı oluşturun
CREATE DATABASE restaurant_pos;

# Kullanıcı oluşturun (opsiyonel)
CREATE USER pos_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE restaurant_pos TO pos_user;

# Çıkış
\q
```

### 4. Ortam Değişkenlerini Ayarlayın
```bash
# .env dosyasını oluşturun
cp .env.example .env
```

`.env` dosyasını düzenleyin:
```env
# Veritabanı bağlantısı
DATABASE_URL="postgresql://username:password@localhost:5432/restaurant_pos?schema=public"

# Uygulama ayarları
NODE_ENV="development"
PORT=3000
JWT_SECRET="your-super-secret-jwt-key-here"
ENCRYPTION_KEY="your-32-character-encryption-key"
```

### 5. Veritabanı Şemasını Oluşturun
```bash
# Prisma client oluştur
npm run db:generate

# Veritabanı şemasını uygula
npm run db:push

# Demo verileri yükle
npm run db:seed
```

### 6. Uygulamayı Başlatın

#### Geliştirme Modu
```bash
# Hem React hem Electron'u başlat
npm run dev
```

#### Sadece React (Web)
```bash
npm run dev:react
```

#### Production Build
```bash
# Build oluştur
npm run build

# Electron uygulamasını paketler
npm run dist
```

## 📁 Proje Yapısı

```
restaurant-pos/
├── electron/                 # Electron ana süreç dosyaları
│   ├── main.ts              # Ana Electron dosyası
│   └── preload.ts           # Preload script
├── prisma/                  # Veritabanı şeması ve migration'lar
│   ├── schema.prisma        # Prisma şeması
│   └── seed.ts              # Demo veri seeder
├── src/
│   ├── components/          # Genel UI bileşenleri
│   │   └── ui/              # shadcn/ui bileşenleri
│   ├── features/            # Feature-based modüller
│   │   ├── auth/            # Kimlik doğrulama
│   │   ├── orders/          # Sipariş yönetimi
│   │   ├── products/        # Ürün yönetimi
│   │   ├── tables/          # Masa yönetimi
│   │   └── reports/         # Raporlama
│   ├── hooks/               # Genel React hooks
│   ├── lib/                 # Yardımcı kütüphaneler
│   │   ├── utils.ts         # Yardımcı fonksiyonlar
│   │   ├── constants.ts     # Sabitler
│   │   ├── trpc.ts          # tRPC client
│   │   └── trpc-client.ts   # tRPC provider
│   ├── server/              # Backend (tRPC)
│   │   ├── routers/         # API route'ları
│   │   ├── services/        # İş mantığı servisleri
│   │   ├── middleware/      # Middleware'ler
│   │   ├── db.ts            # Prisma client
│   │   └── trpc.ts          # tRPC konfigürasyonu
│   ├── types/               # TypeScript tip tanımları
│   ├── App.tsx              # Ana React bileşeni
│   ├── App.css              # Global stiller
│   └── main.tsx             # React entry point
├── package.json             # NPM bağımlılıkları ve scriptler
├── tsconfig.json            # TypeScript konfigürasyonu
├── tailwind.config.js       # Tailwind CSS konfigürasyonu
├── vite.config.ts           # Vite konfigürasyonu
└── README.md                # Bu dosya
```

## 🔧 Geliştirme

### Veritabanı İşlemleri
```bash
# Prisma Studio'yu aç (veritabanı GUI)
npm run db:studio

# Yeni migration oluştur
npm run db:migrate

# Veritabanını sıfırla ve seed'le
npm run db:push && npm run db:seed
```

### Code Quality
```bash
# TypeScript kontrolü
npm run type-check

# ESLint kontrolü
npm run lint

# Build kontrolü
npm run build
```

### Feature Geliştirme
Yeni bir feature eklerken:

1. `src/features/` altında yeni klasör oluşturun
2. Feature klasörü yapısı:
   ```
   feature-name/
   ├── components/          # Feature'a özel bileşenler
   ├── hooks/              # Feature'a özel hooks
   ├── stores/             # Zustand store'ları
   ├── types/              # Feature'a özel tipler
   └── index.ts            # Export dosyası
   ```

### tRPC Router Ekleme
1. `src/server/routers/` altında yeni router oluşturun
2. `src/server/routers/index.ts` dosyasına ekleyin
3. Frontend'de `src/lib/trpc.ts` üzerinden kullanın

## 🎯 Kullanım

### İlk Giriş
Uygulama başladığında demo verilerle birlikte gelir:

**Admin Kullanıcı:**
- Kullanıcı Adı: `admin`
- Şifre: `admin123`
- PIN: `1234`

**Demo Kullanıcılar:**
- Kasiyer: `kasiyer1` / `kasiyer123` / PIN: `1111`
- Garson: `garson1` / `garson123` / PIN: `2222`

### Temel İşlemler

#### Yeni Sipariş Oluşturma
1. Ana ekranda "Yeni Sipariş" butonuna tıklayın
2. Sipariş tipini seçin (Restoranda/Paket/Servis)
3. Masa seçin (restoranda yemek için)
4. Ürünleri sepete ekleyin
5. Siparişi kaydedin

#### Ürün Yönetimi
1. Yönetim panelinden "Ürünler" sekmesine gidin
2. Yeni ürün ekleyin veya mevcut ürünleri düzenleyin
3. Kategoriler ve fiyatları ayarlayın
4. Stok takibi aktif edin (opsiyonel)

#### Masa Yönetimi
1. "Masalar" sekmesinden salon düzenini görüntüleyin
2. Masa durumlarını takip edin
3. Siparişleri masalara atayın

## 🔒 Güvenlik

### Kullanıcı Rolleri ve Yetkiler
- **Süper Admin**: Tüm sistem erişimi
- **Admin**: Şirket düzeyinde yönetim
- **Şube Müdürü**: Şube düzeyinde yönetim
- **Kasiyer**: Satış ve ödeme işlemleri
- **Garson**: Sipariş alma ve masa yönetimi
- **Mutfak**: Sipariş görüntüleme ve durum güncelleme
- **Raporlama**: Sadece rapor görüntüleme

### Veri Güvenliği
- Şifreler bcrypt ile hashlenir
- API iletişimi HTTPS üzerinden
- Hassas veriler için encryption
- Role-based access control (RBAC)
- İşlem logları

## 📊 Performans

### Optimizasyon İpuçları
- Ürün arama: < 100ms
- Sipariş kaydetme: < 500ms
- Ekran geçişleri: < 200ms
- Başlangıç süresi: < 5 saniye

### Veritabanı İndeksleri
Prisma şemasında performans için gerekli indeksler tanımlanmıştır:
- Kullanıcı aramaları için username indeksi
- Ürün aramaları için barcode indeksi
- Sipariş filtreleme için status ve tarih indeksleri

## 🚀 Deployment

### Electron Uygulaması
```bash
# Tüm platformlar için build
npm run dist

# Sadece Windows
npm run dist -- --win

# Sadece macOS
npm run dist -- --mac

# Sadece Linux
npm run dist -- --linux
```

### Web Versiyonu
```bash
# React build
npm run build:react

# Build dosyalarını web sunucusuna deploy edin
```

## 🧪 Test

### Test Komutları
```bash
# Unit testler (gelecekte eklenecek)
npm run test

# E2E testler (gelecekte eklenecek)
npm run test:e2e
```

## 📝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

### Kod Standartları
- TypeScript kullanın
- ESLint kurallarına uyun
- Bileşenler 300 satırı geçmemeli
- Para hesaplamaları için Decimal.js kullanın
- Error handling ekleyin
- Type safety sağlayın (`any` kullanmayın)

## 🐛 Sorun Giderme

### Yaygın Sorunlar

#### Veritabanı Bağlantı Hatası
```bash
# PostgreSQL çalışıyor mu kontrol edin
sudo systemctl status postgresql

# Bağlantı stringini kontrol edin
echo $DATABASE_URL
```

#### Port Çakışması
```bash
# 3000 portunu kullanan süreçleri kontrol edin
lsof -i :3000

# Farklı port kullanın
PORT=3001 npm run dev
```

#### Prisma Client Hatası
```bash
# Prisma client'ı yeniden oluşturun
npm run db:generate
```

#### Build Hatası
```bash
# Node modules'ları temizleyin
rm -rf node_modules package-lock.json
npm install
```

## 📞 Destek

Sorunlarınız için:
1. GitHub Issues sayfasını kontrol edin
2. Yeni issue oluşturun
3. Detaylı açıklama ve hata logları ekleyin

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 🙏 Teşekkürler

Bu proje aşağıdaki açık kaynak projelerden yararlanmaktadır:
- [Electron](https://electronjs.org/)
- [React](https://reactjs.org/)
- [TypeScript](https://typescriptlang.org/)
- [tRPC](https://trpc.io/)
- [Prisma](https://prisma.io/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [TanStack Query](https://tanstack.com/query/)

---

**Restaurant POS System** - Modern restoran yönetimi için geliştirilmiştir.

