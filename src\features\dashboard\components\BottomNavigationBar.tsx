import React, { useState } from 'react'
import { MoreModal } from './MoreModal'

/**
 * Alt navigasyon barı bileşeni
 *
 * Bu bileşen:
 * - "Home", "Orders", "Table", "More" navigasyon seçeneklerini sağlar
 * - Aktif sayfa durumunu gösterir
 * - Merkezdeki restoran butonu ile yeni sipariş oluşturma
 * - Touch-friendly tasarım (44px minimum)
 * - TanStack Router ile navigasyon (gelecekte eklenecek)
 * - "Daha Fazla" butonu modal açar
 */
export function BottomNavigationBar() {
  // TODO: TanStack Router entegrasyonu eklenecek
  const currentPath = '/dashboard' // Şimdilik sabit
  const [isMoreModalOpen, setIsMoreModalOpen] = useState(false)

  const handleNavigation = (path: string) => {
    // TODO: TanStack Router navigate fonksiyonu eklenecek
    console.log('Navigate to:', path)
  }

  const handleCreateOrder = () => {
    // TODO: Yeni sipariş sayfasına yönlendirme
    console.log('Create new order')
  }

  const handleMoreClick = () => {
    setIsMoreModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsMoreModalOpen(false)
  }

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white shadow-[0px_-2px_16px_0px_rgba(0,0,0,0.08)] z-50">
      <div className="relative flex items-center mx-auto max-w-[1194px] px-8 py-1.5 gap-[104px] h-14">
        {/* Left Menu Group */}
        <div className="flex items-center gap-4 flex-1 h-11">
          {/* Home - Active */}
          <button
            onClick={() => handleNavigation('/dashboard')}
            className={`flex items-center justify-center gap-2.5 rounded-2xl flex-1 h-full px-4 py-3 transition-colors min-h-[44px] ${
              currentPath === '/dashboard'
                ? 'bg-[#F0F8FF] text-[#025CCA]'
                : 'text-[#636566] hover:bg-gray-50'
            }`}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.96488 20.3592V14.6552H14.5301V20.3592C14.5301 20.9866 15.0436 21.5 15.6714 21.5H19.0952C19.7229 21.5 20.2365 20.9866 20.2365 20.3592V12.3736H22.1767C22.7017 12.3736 22.9528 11.7234 22.5534 11.3811L13.0121 2.7909C12.5784 2.40303 11.9165 2.40303 11.4828 2.7909L1.94157 11.3811C1.55353 11.7234 1.7932 12.3736 2.3182 12.3736H4.2584V20.3592C4.2584 20.9866 4.77198 21.5 5.39969 21.5H8.82358C9.45129 21.5 9.96488 20.9866 9.96488 20.3592Z"
                fill="currentColor"
              />
            </svg>
            <span className={`text-[14px] leading-[150%] ${
              currentPath === '/dashboard' ? 'font-semibold' : 'font-medium'
            }`}>
              Ana Sayfa
            </span>
          </button>

          {/* Orders */}
          <button
            onClick={() => handleNavigation('/orders')}
            className={`flex items-center justify-center gap-2.5 rounded-2xl flex-1 h-full px-4 py-3 transition-colors min-h-[44px] ${
              currentPath === '/orders'
                ? 'bg-[#F0F8FF] text-[#025CCA]'
                : 'text-[#636566] hover:bg-gray-50'
            }`}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M20.25 3.5L18.75 2L17.25 3.5L15.75 2L14.25 3.5L12.75 2L11.25 3.5L9.75 2L8.25 3.5L6.75 2L5.25 3.5L3.75 2V22L5.25 20.5L6.75 22L8.25 20.5L9.75 22L11.25 20.5L12.75 22L14.25 20.5L15.75 22L17.25 20.5L18.75 22L20.25 20.5L21.75 22V2L20.25 3.5ZM19.75 19.09H5.75V4.91H19.75V19.09ZM6.75 15H18.75V17H6.75V15ZM6.75 11H18.75V13H6.75V11ZM6.75 7H18.75V9H6.75V7Z"
                fill="currentColor"
              />
            </svg>
            <span className={`text-[14px] leading-[150%] ${
              currentPath === '/orders' ? 'font-semibold' : 'font-medium'
            }`}>
              Siparişler
            </span>
          </button>
        </div>

        {/* Right Menu Group */}
        <div className="flex items-center gap-4 flex-1 h-11">
          {/* Table */}
          <button
            onClick={() => handleNavigation('/tables')}
            className={`flex items-center justify-center gap-2.5 rounded-2xl flex-1 h-full px-4 py-3 transition-colors min-h-[44px] ${
              currentPath === '/tables'
                ? 'bg-[#F0F8FF] text-[#025CCA]'
                : 'text-[#636566] hover:bg-gray-50'
            }`}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.75 8H4.75V4H8.75V8ZM7.75 5H5.75V7H7.75V5ZM14.75 20H10.75V16H14.75V20ZM13.75 17H11.75V19H13.75V17ZM8.75 20H4.75V16H8.75V20ZM7.75 17H5.75V19H7.75V17ZM8.75 14H4.75V10H8.75V14ZM7.75 11H5.75V13H7.75V11ZM14.75 14H10.75V10H14.75V14ZM13.75 11H11.75V13H13.75V11ZM16.75 8V4H20.75V8H16.75ZM19.75 7V5H17.75V7H19.75ZM14.75 8H10.75V4H14.75V8ZM13.75 5H11.75V7H13.75V5ZM20.75 14H16.75V10H20.75V14ZM19.75 11H17.75V13H19.75V11ZM20.75 20H16.75V16H20.75V20ZM19.75 17H17.75V19H19.75V17Z"
                fill="currentColor"
              />
            </svg>
            <span className={`text-[14px] leading-[150%] ${
              currentPath === '/tables' ? 'font-semibold' : 'font-medium'
            }`}>
              Masalar
            </span>
          </button>

          {/* More */}
          <button
            onClick={handleMoreClick}
            className={`flex items-center justify-center gap-2.5 rounded-2xl flex-1 h-full px-4 py-3 transition-colors min-h-[44px] ${
              isMoreModalOpen
                ? 'bg-[#F0F8FF] text-[#025CCA]'
                : 'text-[#636566] hover:bg-gray-50'
            }`}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M2.25 12C2.25 6.48 6.73 2 12.25 2C17.77 2 22.25 6.48 22.25 12C22.25 17.52 17.77 22 12.25 22C6.73 22 2.25 17.52 2.25 12ZM4.25 12C4.25 16.42 7.83 20 12.25 20C16.67 20 20.25 16.42 20.25 12C20.25 7.58 16.67 4 12.25 4C7.83 4 4.25 7.58 4.25 12ZM8.75 12C8.75 12.8284 8.07843 13.5 7.25 13.5C6.42157 13.5 5.75 12.8284 5.75 12C5.75 11.1716 6.42157 10.5 7.25 10.5C8.07843 10.5 8.75 11.1716 8.75 12ZM12.25 13.5C13.0784 13.5 13.75 12.8284 13.75 12C13.75 11.1716 13.0784 10.5 12.25 10.5C11.4216 10.5 10.75 11.1716 10.75 12C10.75 12.8284 11.4216 13.5 12.25 13.5ZM18.75 12C18.75 12.8284 18.0784 13.5 17.25 13.5C16.4216 13.5 15.75 12.8284 15.75 12C15.75 11.1716 16.4216 10.5 17.25 10.5C18.0784 10.5 18.75 11.1716 18.75 12Z"
                fill="currentColor"
              />
            </svg>
            <span className={`text-[14px] leading-[150%] ${
              currentPath === '/more' ? 'font-semibold' : 'font-medium'
            }`}>
              Daha Fazla
            </span>
          </button>
        </div>

        {/* Center Restaurant Button */}
        <button
          onClick={handleCreateOrder}
          className="absolute left-1/2 transform -translate-x-1/2 -top-7 w-[72px] h-[72px] bg-[#025CCA] rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M25.3065 22.6665H2.6665V25.3332H29.3332V22.6665H25.3065ZM27.9998 21.3332C27.6398 15.9065 23.6665 11.4665 18.4532 10.3865C18.5865 10.0665 18.6665 9.7065 18.6665 9.33317C18.6665 7.8665 17.4665 6.6665 15.9998 6.6665C14.5332 6.6665 13.3332 7.8665 13.3332 9.33317C13.3332 9.7065 13.4132 10.0665 13.5465 10.3865C8.33317 11.4665 4.35984 15.9065 3.99984 21.3332H27.9998ZM15.9998 12.7732C19.9332 12.7732 23.2932 15.2132 24.6665 18.6532H7.33317C8.7065 15.2132 12.0665 12.7732 15.9998 12.7732Z"
              fill="white"
            />
          </svg>
        </button>
      </div>

      {/* More Modal */}
      <MoreModal
        isOpen={isMoreModalOpen}
        onClose={handleCloseModal}
      />
    </nav>
  )
}
