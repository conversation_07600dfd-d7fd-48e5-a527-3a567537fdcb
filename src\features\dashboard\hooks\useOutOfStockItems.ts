import { trpc } from '../../../lib/trpc-client'
import type { OutOfStockInput } from '../types'

/**
 * Stok kritik ürünleri getiren hook
 * 
 * Bu hook:
 * - Stokta kalmayan veya kritik seviyedeki ürünleri listeler
 * - <PERSON><PERSON><PERSON><PERSON> adı, stok durumu ve tahmini hazır olma zamanını döndürür
 * - Şube filtrelemesi yapar
 * - Limit parametresi ile sonuç sayısını sınırlar
 */
export function useOutOfStockItems(input?: OutOfStockInput) {
  return trpc.dashboard.getOutOfStockItems.useQuery(
    input || { limit: 5 },
    {
      staleTime: 3 * 60 * 1000, // 3 dakika
      refetchInterval: 60 * 1000, // 1 dakikada bir otomatik güncelle
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    }
  )
}
