import React, { useState } from 'react'
import { useSearch, useNavigate, Link } from '@tanstack/react-router'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { ProductIcon, SearchIcon, PlusIcon, ArrowLeftIcon } from '../../../assets/icons'
import { ProductList } from '../components/ProductList'
import { CategoryList } from '../components/CategoryList'
import { ModifierGroupList } from '../components/ModifierGroupList'
import { InventoryItemList } from '../components/InventoryItemList'
import { RecipeList } from '../components/RecipeList'
import { PricingRuleList } from '../components/PricingRuleList'
import { ProductFormModal } from '../components/modals/ProductFormModal'
import { CategoryFormModal } from '../components/modals/CategoryFormModal'
import { useCreateProduct, useUpdateProduct, useDeleteProduct } from '../hooks/useProducts'
import { useCreateCategory, useUpdateCategory, useDeleteCategory } from '../hooks/useCategories'
import { useCreateModifierGroup, useUpdateModifierGroup, useDeleteModifierGroup } from '../hooks/useModifierGroups'

/**
 * Ürün Yönetimi Ana Sayfası
 * 
 * Bu sayfa:
 * - Figma tasarımına sadık kalarak ürün yönetimi arayüzünü sağlar
 * - 5 sekme içerir: Ürünler, Kategoriler, Modifiyerler, Envanter, Fiyatlandırma
 * - Responsive tasarım ve touch-friendly UI
 * - Arama çubuğu ve yeni ekleme butonları
 * - Full viewport coverage (margin/padding sıfır)
 */
export function ProductManagementPage() {
  const search = useSearch({ from: '/products' })
  const navigate = useNavigate({ from: '/products' })

  const activeTab = search.tab || 'products'
  const searchQuery = search.search || ''

  const handleTabChange = (tab: string) => {
    navigate({
      search: (prev) => ({ ...prev, tab }),
    })
  }

  const handleSearchChange = (query: string) => {
    navigate({
      search: (prev) => ({ ...prev, search: query || undefined }),
    })
  }

  return (
    <div className="min-h-screen bg-neutral-100 w-full">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-6 py-6">
        <div className="flex items-center gap-4 mb-4">
          {/* Back to Dashboard Button */}
          <Link
            to="/"
            className="bg-gray-100 hover:bg-gray-200 rounded-2xl p-3 transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </Link>

          <div className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] rounded-2xl p-3 shadow-lg">
            <ProductIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-[#202325] leading-8">
              Ürün Yönetimi
            </h1>
            <p className="text-sm text-[#636566] leading-5 mt-1">
              Menü öğelerinizi yönetin ve düzenleyin
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex justify-end">
          <div className="relative w-80">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="w-5 h-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Ürün, kategori veya modifiyer ara..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 h-12 rounded-2xl border-gray-200 bg-white"
            />
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white border-b border-gray-200">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="h-auto bg-transparent p-0 gap-0 ml-6">
            <TabsTrigger 
              value="products" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm"
            >
              Ürünler
            </TabsTrigger>
            <TabsTrigger 
              value="categories" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Kategoriler
            </TabsTrigger>
            <TabsTrigger 
              value="modifiers" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Modifiyerler
            </TabsTrigger>
            <TabsTrigger 
              value="inventory" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Envanter
            </TabsTrigger>
            <TabsTrigger 
              value="pricing" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Fiyatlandırma
            </TabsTrigger>
          </TabsList>

          {/* Tab Contents */}
          <TabsContent value="products" className="mt-0">
            <ProductsTabContent />
          </TabsContent>
          
          <TabsContent value="categories" className="mt-0">
            <CategoriesTabContent />
          </TabsContent>
          
          <TabsContent value="modifiers" className="mt-0">
            <ModifiersTabContent />
          </TabsContent>
          
          <TabsContent value="inventory" className="mt-0">
            <InventoryTabContent />
          </TabsContent>
          
          <TabsContent value="pricing" className="mt-0">
            <PricingTabContent />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Tab content components with real CRUD operations
function ProductsTabContent() {
  const [searchQuery, setSearchQuery] = useState('')
  const [isProductModalOpen, setIsProductModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<any>(null)

  // Mutations
  const createProductMutation = useCreateProduct()
  const updateProductMutation = useUpdateProduct()
  const deleteProductMutation = useDeleteProduct()

  const handleProductEdit = (productId: string) => {
    // TODO: Fetch product data by ID
    setEditingProduct({ id: productId })
    setIsProductModalOpen(true)
  }

  const handleProductDelete = async (productId: string) => {
    if (window.confirm('Bu ürünü silmek istediğinizden emin misiniz?')) {
      try {
        await deleteProductMutation.mutateAsync({ id: productId })
      } catch (error) {
        console.error('Ürün silme hatası:', error)
      }
    }
  }

  const handleNewProduct = () => {
    setEditingProduct(null)
    setIsProductModalOpen(true)
  }

  const handleProductSubmit = async (data: any) => {
    try {
      if (editingProduct) {
        await updateProductMutation.mutateAsync({ id: editingProduct.id, ...data })
      } else {
        await createProductMutation.mutateAsync(data)
      }
      setIsProductModalOpen(false)
      setEditingProduct(null)
    } catch (error) {
      console.error('Ürün kaydetme hatası:', error)
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Button
          onClick={handleNewProduct}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Ürün Ekle
        </Button>
      </div>

      <ProductList
        searchQuery={searchQuery}
        onProductEdit={handleProductEdit}
        onProductDelete={handleProductDelete}
      />

      {/* Product Form Modal */}
      <ProductFormModal
        isOpen={isProductModalOpen}
        onClose={() => {
          setIsProductModalOpen(false)
          setEditingProduct(null)
        }}
        onSubmit={handleProductSubmit}
        initialData={editingProduct}
        mode={editingProduct ? 'edit' : 'create'}
        isLoading={createProductMutation.isPending || updateProductMutation.isPending}
      />
    </div>
  )
}

function CategoriesTabContent() {
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<any>(null)

  // Mutations
  const createCategoryMutation = useCreateCategory()
  const updateCategoryMutation = useUpdateCategory()
  const deleteCategoryMutation = useDeleteCategory()

  const handleCategoryEdit = (categoryId: string) => {
    // TODO: Fetch category data by ID
    setEditingCategory({ id: categoryId })
    setIsCategoryModalOpen(true)
  }

  const handleCategoryDelete = async (categoryId: string) => {
    if (window.confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')) {
      try {
        await deleteCategoryMutation.mutateAsync({ id: categoryId })
      } catch (error) {
        console.error('Kategori silme hatası:', error)
      }
    }
  }

  const handleNewCategory = () => {
    setEditingCategory(null)
    setIsCategoryModalOpen(true)
  }

  const handleCategorySubmit = async (data: any) => {
    try {
      if (editingCategory) {
        await updateCategoryMutation.mutateAsync({ id: editingCategory.id, ...data })
      } else {
        await createCategoryMutation.mutateAsync(data)
      }
      setIsCategoryModalOpen(false)
      setEditingCategory(null)
    } catch (error) {
      console.error('Kategori kaydetme hatası:', error)
    }
  }

  return (
    <div className="p-6">
      <CategoryList
        onCategoryEdit={handleCategoryEdit}
        onCategoryDelete={handleCategoryDelete}
        onNewCategory={handleNewCategory}
      />

      {/* Category Form Modal */}
      <CategoryFormModal
        isOpen={isCategoryModalOpen}
        onClose={() => {
          setIsCategoryModalOpen(false)
          setEditingCategory(null)
        }}
        onSubmit={handleCategorySubmit}
        initialData={editingCategory}
        mode={editingCategory ? 'edit' : 'create'}
        isLoading={createCategoryMutation.isPending || updateCategoryMutation.isPending}
      />
    </div>
  )
}

function ModifiersTabContent() {
  // Mutations
  const deleteModifierGroupMutation = useDeleteModifierGroup()

  const handleGroupEdit = (groupId: string) => {
    console.log('Edit modifier group:', groupId)
    // TODO: ModifierGroup modal açma işlemi
  }

  const handleGroupDelete = async (groupId: string) => {
    if (window.confirm('Bu modifiyer grubunu silmek istediğinizden emin misiniz?')) {
      try {
        await deleteModifierGroupMutation.mutateAsync({ id: groupId })
      } catch (error) {
        console.error('Modifiyer grup silme hatası:', error)
      }
    }
  }

  const handleModifierEdit = (modifierId: string) => {
    console.log('Edit modifier:', modifierId)
    // TODO: Modifier modal açma işlemi
  }

  const handleModifierDelete = (modifierId: string) => {
    console.log('Delete modifier:', modifierId)
    // TODO: Modifier silme onayı ve işlemi
  }

  const handleNewGroup = () => {
    console.log('New modifier group')
    // TODO: Yeni grup modal açma
  }

  const handleNewModifier = (groupId: string) => {
    console.log('New modifier for group:', groupId)
    // TODO: Yeni modifiyer modal açma
  }

  return (
    <div className="p-6">
      <ModifierGroupList
        onGroupEdit={handleGroupEdit}
        onGroupDelete={handleGroupDelete}
        onModifierEdit={handleModifierEdit}
        onModifierDelete={handleModifierDelete}
        onNewGroup={handleNewGroup}
        onNewModifier={handleNewModifier}
      />
    </div>
  )
}

function InventoryTabContent() {
  const [activeSubTab, setActiveSubTab] = useState<'items' | 'recipes'>('items')

  const handleItemEdit = (itemId: string) => {
    console.log('Edit inventory item:', itemId)
    // TODO: Modal açma işlemi
  }

  const handleItemDelete = (itemId: string) => {
    console.log('Delete inventory item:', itemId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewItem = () => {
    console.log('New inventory item')
    // TODO: Yeni envanter kalemi modal açma
  }

  const handleRecipeEdit = (recipeId: string) => {
    console.log('Edit recipe:', recipeId)
    // TODO: Modal açma işlemi
  }

  const handleRecipeDelete = (recipeId: string) => {
    console.log('Delete recipe:', recipeId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewRecipe = () => {
    console.log('New recipe')
    // TODO: Yeni reçete modal açma
  }

  return (
    <div className="p-6">
      {/* Sub Tabs */}
      <div className="mb-6">
        <div className="flex gap-2 border-b border-gray-200">
          <Button
            variant={activeSubTab === 'items' ? 'default' : 'ghost'}
            onClick={() => setActiveSubTab('items')}
            className="rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca]"
          >
            Envanter Kalemleri
          </Button>
          <Button
            variant={activeSubTab === 'recipes' ? 'default' : 'ghost'}
            onClick={() => setActiveSubTab('recipes')}
            className="rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca]"
          >
            Reçeteler
          </Button>
        </div>
      </div>

      {/* Content */}
      {activeSubTab === 'items' ? (
        <InventoryItemList
          onItemEdit={handleItemEdit}
          onItemDelete={handleItemDelete}
          onNewItem={handleNewItem}
        />
      ) : (
        <RecipeList
          onRecipeEdit={handleRecipeEdit}
          onRecipeDelete={handleRecipeDelete}
          onNewRecipe={handleNewRecipe}
        />
      )}
    </div>
  )
}

function PricingTabContent() {
  const handleRuleEdit = (ruleId: string) => {
    console.log('Edit pricing rule:', ruleId)
    // TODO: Modal açma işlemi
  }

  const handleRuleDelete = (ruleId: string) => {
    console.log('Delete pricing rule:', ruleId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewRule = () => {
    console.log('New pricing rule')
    // TODO: Yeni kural modal açma
  }

  return (
    <div className="p-6">
      <PricingRuleList
        onRuleEdit={handleRuleEdit}
        onRuleDelete={handleRuleDelete}
        onNewRule={handleNewRule}
      />
    </div>
  )
}
