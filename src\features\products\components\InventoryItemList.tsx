import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { SearchIcon, EditIcon, PlusIcon, StockIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'

/**
 * Envanter Kalemi Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Envanter kalemlerini listeler
 * - Stok durumu ve kritik seviye gösterimi
 * - Arama ve filtreleme
 * - Birim ve maliyet bilgileri
 * - Touch-friendly tasarım
 */

interface InventoryItem {
  id: string
  name: string
  description?: string
  unit: string
  currentStock: string
  criticalStock?: string
  costPerUnit: string
  supplier?: string
  lastUpdated: string
  active: boolean
}

interface InventoryItemListProps {
  searchQuery?: string
  onItemEdit?: (itemId: string) => void
  onItemDelete?: (itemId: string) => void
  onNewItem?: () => void
}

// Mock data - gerçek API'den gelecek
const mockInventoryItems: InventoryItem[] = [
  {
    id: '1',
    name: 'Domates',
    description: 'Taze domates - kg',
    unit: 'kg',
    currentStock: '25.5',
    criticalStock: '10.0',
    costPerUnit: '3.50',
    supplier: 'Yerel Çiftçi',
    lastUpdated: '2024-01-15',
    active: true
  },
  {
    id: '2',
    name: 'Mozzarella Peyniri',
    description: 'İtalyan mozzarella peyniri',
    unit: 'kg',
    currentStock: '8.2',
    criticalStock: '5.0',
    costPerUnit: '45.00',
    supplier: 'Süt Ürünleri A.Ş.',
    lastUpdated: '2024-01-14',
    active: true
  },
  {
    id: '3',
    name: 'Zeytinyağı',
    description: 'Sızma zeytinyağı',
    unit: 'litre',
    currentStock: '2.1',
    criticalStock: '5.0',
    costPerUnit: '85.00',
    supplier: 'Ege Zeytinyağı',
    lastUpdated: '2024-01-13',
    active: true
  },
  {
    id: '4',
    name: 'Un',
    description: 'Beyaz un - tip 550',
    unit: 'kg',
    currentStock: '45.0',
    criticalStock: '20.0',
    costPerUnit: '2.80',
    supplier: 'Un Fabrikası',
    lastUpdated: '2024-01-15',
    active: true
  },
  {
    id: '5',
    name: 'Tavuk Göğsü',
    description: 'Bonfile tavuk göğsü',
    unit: 'kg',
    currentStock: '12.5',
    criticalStock: '8.0',
    costPerUnit: '28.00',
    supplier: 'Et Tedarikçisi',
    lastUpdated: '2024-01-15',
    active: true
  }
]

export function InventoryItemList({ searchQuery = '', onItemEdit, onItemDelete, onNewItem }: InventoryItemListProps) {
  const [localSearch, setLocalSearch] = useState('')
  const [stockFilter, setStockFilter] = useState<'all' | 'critical' | 'low'>('all')
  
  // 300ms debounce for search
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)
  
  // Filtrelenmiş envanter kalemleri
  const filteredItems = useMemo(() => {
    let items = mockInventoryItems
    
    // Arama filtresi
    if (debouncedSearch) {
      items = items.filter(item => 
        item.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        item.description?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        item.supplier?.toLowerCase().includes(debouncedSearch.toLowerCase())
      )
    }
    
    // Stok durumu filtresi
    if (stockFilter === 'critical') {
      items = items.filter(item => {
        const current = parseFloat(item.currentStock)
        const critical = parseFloat(item.criticalStock || '0')
        return current <= critical
      })
    } else if (stockFilter === 'low') {
      items = items.filter(item => {
        const current = parseFloat(item.currentStock)
        const critical = parseFloat(item.criticalStock || '0')
        return current <= critical * 1.5 && current > critical
      })
    }
    
    return items
  }, [debouncedSearch, stockFilter])
  
  const getStockStatus = (item: InventoryItem) => {
    const current = parseFloat(item.currentStock)
    const critical = parseFloat(item.criticalStock || '0')
    
    if (current <= critical) {
      return { status: 'critical', color: 'bg-red-500', text: 'Kritik' }
    } else if (current <= critical * 1.5) {
      return { status: 'low', color: 'bg-yellow-500', text: 'Düşük' }
    } else {
      return { status: 'good', color: 'bg-green-500', text: 'İyi' }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Envanter Kalemleri</h2>
          <p className="text-sm text-[#636566]">{filteredItems.length} kalem bulundu</p>
        </div>
        
        <Button 
          onClick={onNewItem}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Kalem Ekle
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        {/* Search Bar */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <SearchIcon className="w-5 h-5 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="Envanter kalemi ara..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="pl-10 h-10 rounded-xl border-gray-200"
          />
        </div>
        
        {/* Stock Filter */}
        <div className="flex gap-2">
          <Button
            variant={stockFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('all')}
            className="h-10 px-4"
          >
            Tümü
          </Button>
          <Button
            variant={stockFilter === 'critical' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('critical')}
            className="h-10 px-4 text-red-600 border-red-200 hover:bg-red-50"
          >
            Kritik
          </Button>
          <Button
            variant={stockFilter === 'low' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('low')}
            className="h-10 px-4 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
          >
            Düşük
          </Button>
        </div>
      </div>

      {/* Inventory Items */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Envanter kalemi bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredItems.map((item) => (
            <InventoryItemCard
              key={item.id}
              item={item}
              onEdit={() => onItemEdit?.(item.id)}
              onDelete={() => onItemDelete?.(item.id)}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Inventory Item Card Component
interface InventoryItemCardProps {
  item: InventoryItem
  onEdit: () => void
  onDelete: () => void
}

function InventoryItemCard({ item, onEdit, onDelete }: InventoryItemCardProps) {
  const stockStatus = getStockStatus(item)
  
  function getStockStatus(item: InventoryItem) {
    const current = parseFloat(item.currentStock)
    const critical = parseFloat(item.criticalStock || '0')
    
    if (current <= critical) {
      return { status: 'critical', color: 'bg-red-500', text: 'Kritik' }
    } else if (current <= critical * 1.5) {
      return { status: 'low', color: 'bg-yellow-500', text: 'Düşük' }
    } else {
      return { status: 'good', color: 'bg-green-500', text: 'İyi' }
    }
  }

  return (
    <Card className="rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-5">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-bold text-[#202325] text-lg mb-1">{item.name}</h3>
            {item.description && (
              <p className="text-[#636566] text-sm line-clamp-1">{item.description}</p>
            )}
          </div>
          
          {/* Stock Status Badge */}
          <div className="flex items-center gap-1 ml-2">
            <div className={`w-2 h-2 rounded-full ${stockStatus.color}`}></div>
            <span className="text-xs font-medium text-gray-600">{stockStatus.text}</span>
          </div>
        </div>

        {/* Stock Info */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#636566]">Mevcut Stok:</span>
            <span className="font-semibold text-[#202325]">{item.currentStock} {item.unit}</span>
          </div>
          
          {item.criticalStock && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#636566]">Kritik Seviye:</span>
              <span className="text-sm text-red-600">{item.criticalStock} {item.unit}</span>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#636566]">Birim Maliyet:</span>
            <span className="font-semibold text-[#025cca]">${item.costPerUnit}</span>
          </div>
          
          {item.supplier && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-[#636566]">Tedarikçi:</span>
              <span className="text-sm text-[#202325] line-clamp-1">{item.supplier}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={onEdit}
            className="flex-1 bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 font-semibold"
            variant="outline"
          >
            <EditIcon className="w-4 h-4 mr-1" />
            Düzenle
          </Button>
          <Button
            onClick={onDelete}
            variant="outline"
            className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3 font-semibold"
          >
            Sil
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
