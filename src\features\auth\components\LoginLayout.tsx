import React from 'react'

// Placeholder background - gerçek projede restaurant görseli kullanılacak
const backgroundImage = "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"

interface LoginLayoutProps {
  children: React.ReactNode
  quote?: string
  author?: string
}

export function LoginLayout({ 
  children, 
  quote = "Every contact we have with a customer influences whether or not they'll come back. We have to be great every time or we'll lose them.",
  author = "<PERSON>, <PERSON>"
}: LoginLayoutProps) {
  return (
    <div className="bg-white relative w-full h-screen flex">
      {/* Sol taraf - Görsel alan */}
      <div
        className="relative w-[804px] h-full hidden lg:block"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0) 36.811%, rgba(0, 0, 0, 0.66) 100%), url('${backgroundImage}')`,
          backgroundSize: 'auto, cover',
          backgroundPosition: '0% 0%, 50% 50%',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Slider indicator */}
        <div className="absolute bottom-[4.796%] left-[3.35%] w-[48px] h-[6px]">
          <div className="flex gap-1 h-full">
            <div className="bg-white rounded-[3px] flex-1" />
            <div className="bg-[#a2a4a4] rounded-[3px] flex-1" />
            <div className="bg-[#a2a4a4] rounded-[3px] flex-1" />
          </div>
        </div>

        {/* Quote section */}
        <div className="absolute left-[48px] top-[560px] w-[670px]">
          {/* Quote mark icon */}
          <div className="w-5 h-[17px] mb-[14px]">
            <svg className="w-5 h-[17px]" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0 17H8L6 9H2C2 4 4 2 8 2V0C2 0 0 4 0 9V17ZM12 17H20L18 9H14C14 4 16 2 20 2V0C14 0 12 4 12 9V17Z" fill="white"/>
            </svg>
          </div>
          
          {/* Quote text */}
          <p className="font-inter font-medium text-[24px] leading-[36px] text-white mb-[42px]">
            {quote}
          </p>

          {/* Author */}
          <div className="inline-flex items-center justify-center px-[18px] py-2 rounded-[99px] border border-white">
            <span className="font-inter font-medium text-[14px] leading-[21px] text-white">
              {author}
            </span>
          </div>
        </div>
      </div>

      {/* Sağ taraf - Login formu */}
      <div className="flex-1 bg-white">
        {children}
      </div>
    </div>
  )
}
