import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type { 
  PriceOverrideCreateInput,
  PriceOverrideUpdateInput,
  PriceOverrideListInput,
  PriceOverrideOutput,
  PriceOverrideListOutput
} from '../../lib/validations/products'

/**
 * PriceOverrideService - Fiyat geçersiz kılma yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Fiyat geçersiz kılma CRUD işlemleri
 * - Zaman bazlı fiyat yönetimi
 * - Şube bazlı fiyat yönetimi
 * - Çakışma kontrolü
 * - Decimal.js ile güvenli para hesaplamaları
 */
export class PriceOverrideService {
  /**
   * Fiyat geçersiz kılma listesini getirir
   * @param input - Filtreleme ve sayfalama parametreleri
   * @param companyId - Şirket ID'si
   * @returns Fiyat geçersiz kılma listesi
   */
  static async getPriceOverrides(
    input: PriceOverrideListInput,
    companyId: string
  ): Promise<PriceOverrideListOutput> {
    const { 
      productId,
      branchId,
      active,
      includeExpired = false,
      page = 1, 
      limit = 10 
    } = input

    const skip = (page - 1) * limit
    const now = new Date()

    // Where koşulları
    const where = {
      product: { companyId },
      ...(productId && { productId }),
      ...(branchId && { branchId }),
      ...(active !== undefined && { active }),
      ...(!includeExpired && {
        OR: [
          { endDate: null },
          { endDate: { gte: now } },
        ],
      }),
    }

    // Fiyat geçersiz kılmaları getir
    const [priceOverrides, total] = await Promise.all([
      prisma.priceOverride.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              basePrice: true,
            },
          },
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: { startDate: 'desc' },
        skip: limit ? skip : undefined,
        take: limit || undefined,
      }),
      prisma.priceOverride.count({ where }),
    ])

    const totalPages = limit ? Math.ceil(total / limit) : 1

    // Decimal değerleri string'e çevir
    const formattedPriceOverrides = priceOverrides.map(override => ({
      ...override,
      price: override.price.toString(),
      product: override.product ? {
        ...override.product,
        basePrice: override.product.basePrice.toString(),
      } : undefined,
    }))

    return {
      priceOverrides: formattedPriceOverrides as PriceOverrideOutput[],
      total,
      page,
      limit: limit || total,
      totalPages,
    }
  }

  /**
   * ID'ye göre fiyat geçersiz kılma getirir
   * @param id - Fiyat geçersiz kılma ID'si
   * @param companyId - Şirket ID'si
   * @returns Fiyat geçersiz kılma bilgisi
   */
  static async getPriceOverrideById(
    id: string,
    companyId: string
  ): Promise<PriceOverrideOutput> {
    const priceOverride = await prisma.priceOverride.findFirst({
      where: { 
        id,
        product: { companyId },
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            code: true,
            basePrice: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    if (!priceOverride) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Fiyat geçersiz kılma bulunamadı',
      })
    }

    // Decimal değerleri string'e çevir
    const formattedPriceOverride = {
      ...priceOverride,
      price: priceOverride.price.toString(),
      product: priceOverride.product ? {
        ...priceOverride.product,
        basePrice: priceOverride.product.basePrice.toString(),
      } : undefined,
    }

    return formattedPriceOverride as PriceOverrideOutput
  }

  /**
   * Yeni fiyat geçersiz kılma oluşturur
   * @param input - Fiyat geçersiz kılma bilgileri
   * @param companyId - Şirket ID'si
   * @returns Oluşturulan fiyat geçersiz kılma ID'si
   */
  static async createPriceOverride(
    input: PriceOverrideCreateInput,
    companyId: string
  ): Promise<string> {
    const {
      productId,
      branchId,
      price,
      startDate,
      endDate,
      startTime,
      endTime,
      daysOfWeek,
      active,
    } = input

    // Ürün kontrolü
    const product = await prisma.product.findFirst({
      where: { id: productId, companyId },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Şube kontrolü
    if (branchId) {
      const branch = await prisma.branch.findFirst({
        where: { id: branchId, companyId },
      })

      if (!branch) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Şube bulunamadı',
        })
      }
    }

    // Tarih kontrolü
    if (startDate && endDate && startDate > endDate) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Başlangıç tarihi bitiş tarihinden sonra olamaz',
      })
    }

    // Saat kontrolü
    if (startTime && endTime && startTime >= endTime) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Başlangıç saati bitiş saatinden sonra olamaz',
      })
    }

    // Haftanın günleri kontrolü
    if (daysOfWeek && daysOfWeek.some(day => day < 1 || day > 7)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Geçersiz haftanın günü (1-7 arası olmalı)',
      })
    }

    // Çakışan fiyat geçersiz kılma kontrolü
    const conflictingOverride = await this.checkPriceOverrideConflict(
      productId,
      branchId
    )

    if (conflictingOverride) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu zaman aralığında zaten bir fiyat geçersiz kılma mevcut',
      })
    }

    // Fiyat geçersiz kılma oluştur
    const priceOverride = await prisma.priceOverride.create({
      data: {
        productId,
        branchId,
        price: new Decimal(price),
        startDate,
        endDate,
        startTime,
        endTime,
        daysOfWeek: daysOfWeek || [],
        active,
      },
    })

    return priceOverride.id
  }

  /**
   * Fiyat geçersiz kılma günceller
   * @param input - Güncellenecek fiyat geçersiz kılma bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async updatePriceOverride(
    input: PriceOverrideUpdateInput,
    companyId: string
  ): Promise<boolean> {
    const { id, ...updateData } = input

    // Fiyat geçersiz kılma varlık kontrolü
    const existingOverride = await prisma.priceOverride.findFirst({
      where: { 
        id,
        product: { companyId },
      },
    })

    if (!existingOverride) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Fiyat geçersiz kılma bulunamadı',
      })
    }

    // Şube kontrolü
    if (updateData.branchId) {
      const branch = await prisma.branch.findFirst({
        where: { id: updateData.branchId, companyId },
      })

      if (!branch) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Şube bulunamadı',
        })
      }
    }

    // Tarih kontrolü
    const startDate = updateData.startDate ?? existingOverride.startDate
    const endDate = updateData.endDate ?? existingOverride.endDate

    if (startDate && endDate && startDate > endDate) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Başlangıç tarihi bitiş tarihinden sonra olamaz',
      })
    }

    // Saat kontrolü
    const startTime = updateData.startTime ?? existingOverride.startTime
    const endTime = updateData.endTime ?? existingOverride.endTime

    if (startTime && endTime && startTime >= endTime) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Başlangıç saati bitiş saatinden sonra olamaz',
      })
    }

    // Haftanın günleri kontrolü
    if (updateData.daysOfWeek && updateData.daysOfWeek.some(day => day < 1 || day > 7)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Geçersiz haftanın günü (1-7 arası olmalı)',
      })
    }

    // Decimal dönüşümü
    const processedUpdateData = {
      ...updateData,
      ...(updateData.price && { price: new Decimal(updateData.price) }),
    }

    // Fiyat geçersiz kılma güncelle
    await prisma.priceOverride.update({
      where: { id },
      data: processedUpdateData,
    })

    return true
  }

  /**
   * Fiyat geçersiz kılma siler
   * @param id - Fiyat geçersiz kılma ID'si
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async deletePriceOverride(id: string, companyId: string): Promise<boolean> {
    // Fiyat geçersiz kılma varlık kontrolü
    const priceOverride = await prisma.priceOverride.findFirst({
      where: { 
        id,
        product: { companyId },
      },
    })

    if (!priceOverride) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Fiyat geçersiz kılma bulunamadı',
      })
    }

    // Fiyat geçersiz kılma sil
    await prisma.priceOverride.delete({
      where: { id },
    })

    return true
  }

  /**
   * Fiyat geçersiz kılma çakışma kontrolü yapar
   * @param productId - Ürün ID'si
   * @param branchId - Şube ID'si
   * @returns Çakışan fiyat geçersiz kılma var mı?
   */
  private static async checkPriceOverrideConflict(
    productId: string,
    branchId?: string
  ): Promise<boolean> {
    // Basit çakışma kontrolü - aynı ürün ve şube için aktif fiyat geçersiz kılma var mı?
    const existingOverride = await prisma.priceOverride.findFirst({
      where: {
        productId,
        branchId,
        active: true,
      },
    })

    return !!existingOverride
  }

  /**
   * Ürün için geçerli fiyatı hesaplar
   * @param productId - Ürün ID'si
   * @param branchId - Şube ID'si (opsiyonel)
   * @param checkTime - Kontrol zamanı (varsayılan: şimdi)
   * @returns Geçerli fiyat
   */
  static async getEffectivePrice(
    productId: string,
    branchId?: string,
    checkTime: Date = new Date()
  ): Promise<string> {
    // Ürünün base fiyatını getir
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { basePrice: true },
    })

    if (!product) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Ürün bulunamadı',
      })
    }

    // Geçerli fiyat geçersiz kılma var mı kontrol et
    const currentDay = checkTime.getDay() || 7 // Pazar = 7
    const currentTime = checkTime.toTimeString().slice(0, 5) // HH:MM format

    const priceOverride = await prisma.priceOverride.findFirst({
      where: {
        productId,
        branchId,
        active: true,
        AND: [
          {
            OR: [
              { startDate: null, endDate: null },
              {
                startDate: { lte: checkTime },
                endDate: { gte: checkTime },
              },
            ],
          },
          {
            OR: [
              { startTime: null, endTime: null },
              {
                startTime: { lte: currentTime },
                endTime: { gte: currentTime },
              },
            ],
          },
          {
            OR: [
              { daysOfWeek: { isEmpty: true } },
              { daysOfWeek: { has: currentDay } },
            ],
          },
        ],
      },
      orderBy: { startDate: 'desc' },
    })

    // Fiyat geçersiz kılma varsa onu kullan, yoksa base fiyatı kullan
    const effectivePrice = priceOverride ? priceOverride.price : product.basePrice

    return effectivePrice.toString()
  }
}
