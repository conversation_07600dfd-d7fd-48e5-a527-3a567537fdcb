import { HeaderBar } from "../components/HeaderBar"
import { GreetingSection } from "../components/GreetingSection"
import { OverviewCards } from "../components/OverviewCards"
import { PopularDishes } from "../components/PopularDishes"
import { OutOfStockItems } from "../components/OutOfStockItems"
import { OrderPanel } from "../components/OrderPanel"
import { BottomNavigationBar } from "../components/BottomNavigationBar"

/**
 * Ana Dashboard sayfası - Figma tasarımına uygun 12-column grid layout
 *
 * Bu sayfa:
 * - Figma tasarımına birebir uygun layout (dashboard hazır ui referansı)
 * - 12-column CSS Grid sistemi kullanır
 * - Header: Full width (12 columns)
 * - Left Sidebar: Metrics & Lists (8 columns)
 * - Right Sidebar: Order Panel (4 columns)
 * - Footer: Full width (12 columns)
 * - Responsive layout (mobile'da tek sütun)
 * - Touch-friendly tasarım (44px minimum)
 */
export function DashboardPage() {
  return (
    <div className="dashboard-container bg-[#F5F5F5]">


      {/* Header - Full Width (12 columns) */}
      <div className="dashboard-header">
        <HeaderBar />
      </div>

      {/* Left Sidebar - Dashboard Metrics & Lists (8 columns) */}
      <div className="dashboard-sidebar mr-8 px-5 py-6 space-y-6">
        {/* Greetings & Time Section */}
        <GreetingSection />

        {/* Overview Cards - 3 columns grid within the 8-column area */}
        <div className="mb-6">
          <OverviewCards />
        </div>

        {/* Popular Dishes & Out of Stock - 2 columns grid within the 8-column area */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <PopularDishes />
          <OutOfStockItems />
        </div>
      </div>

      {/* Right Sidebar - Order Panel (4 columns) */}
      <div className="dashboard-main pr-8 py-6">
        <OrderPanel />
      </div>

      {/* Footer Navigation - Full Width (12 columns) */}
      <div className="dashboard-footer">
        <BottomNavigationBar />
      </div>
    </div>
  )
}
