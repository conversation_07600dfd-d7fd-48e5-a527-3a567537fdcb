import { TRPCError } from '@trpc/server'
import { Decimal } from 'decimal.js'
import { prisma } from '../db'
import type { 
  TaxCreateInput,
  TaxUpdateInput,
  TaxOutput,
  TaxListOutput
} from '../../lib/validations/products'

/**
 * TaxService - Vergi yönetimi için servis katmanı
 * 
 * Bu servis aşağıdaki işlemleri gerçekleştirir:
 * - Vergi CRUD işlemleri
 * - Varsayılan vergi yönetimi
 * - Decimal.js ile güvenli para hesaplamaları
 */
export class TaxService {
  /**
   * Vergi listesini getirir
   * @param companyId - Şirket ID'si
   * @returns Vergi listesi
   */
  static async getTaxes(companyId: string): Promise<TaxListOutput> {
    const taxes = await prisma.tax.findMany({
      where: { companyId },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    })

    const formattedTaxes = taxes.map(tax => ({
      ...tax,
      rate: tax.rate.toString(),
    }))

    return {
      taxes: formattedTaxes as TaxOutput[],
      total: taxes.length,
    }
  }

  /**
   * ID'ye göre vergi getirir
   * @param id - Vergi ID'si
   * @param companyId - Şirket ID'si
   * @returns Vergi bilgisi
   */
  static async getTaxById(id: string, companyId: string): Promise<TaxOutput> {
    const tax = await prisma.tax.findFirst({
      where: { id, companyId },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    })

    if (!tax) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Vergi oranı bulunamadı',
      })
    }

    return {
      ...tax,
      rate: tax.rate.toString(),
    } as TaxOutput
  }

  /**
   * Yeni vergi oluşturur
   * @param input - Vergi bilgileri
   * @param companyId - Şirket ID'si
   * @returns Oluşturulan vergi ID'si
   */
  static async createTax(input: TaxCreateInput, companyId: string): Promise<string> {
    const { name, rate, code, isDefault, active } = input

    // Aynı kodda vergi kontrolü
    const existingTax = await prisma.tax.findFirst({
      where: {
        companyId,
        code,
      },
    })

    if (existingTax) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Bu kodda bir vergi oranı zaten mevcut',
      })
    }

    // Eğer varsayılan vergi ise, diğerlerini varsayılan olmaktan çıkar
    if (isDefault) {
      await prisma.tax.updateMany({
        where: { companyId, isDefault: true },
        data: { isDefault: false },
      })
    }

    // Vergi oluştur
    const tax = await prisma.tax.create({
      data: {
        companyId,
        name,
        rate: new Decimal(rate),
        code,
        isDefault,
        active,
      },
    })

    return tax.id
  }

  /**
   * Vergi günceller
   * @param input - Güncellenecek vergi bilgileri
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async updateTax(input: TaxUpdateInput, companyId: string): Promise<boolean> {
    const { id, ...updateData } = input

    // Vergi varlık kontrolü
    const existingTax = await prisma.tax.findFirst({
      where: { id, companyId },
    })

    if (!existingTax) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Vergi oranı bulunamadı',
      })
    }

    // Kod değişikliği kontrolü
    if (updateData.code && updateData.code !== existingTax.code) {
      const duplicateTax = await prisma.tax.findFirst({
        where: {
          companyId,
          code: updateData.code,
          id: { not: id },
        },
      })

      if (duplicateTax) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Bu kodda bir vergi oranı zaten mevcut',
        })
      }
    }

    // Eğer varsayılan vergi ise, diğerlerini varsayılan olmaktan çıkar
    if (updateData.isDefault) {
      await prisma.tax.updateMany({
        where: { companyId, isDefault: true, id: { not: id } },
        data: { isDefault: false },
      })
    }

    // Decimal dönüşümü
    const processedUpdateData = {
      ...updateData,
      ...(updateData.rate && { rate: new Decimal(updateData.rate) }),
    }

    // Vergi güncelle
    await prisma.tax.update({
      where: { id },
      data: processedUpdateData,
    })

    return true
  }

  /**
   * Vergi siler
   * @param id - Vergi ID'si
   * @param companyId - Şirket ID'si
   * @returns Başarı durumu
   */
  static async deleteTax(id: string, companyId: string): Promise<boolean> {
    // Vergi varlık kontrolü
    const tax = await prisma.tax.findFirst({
      where: { id, companyId },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    })

    if (!tax) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Vergi oranı bulunamadı',
      })
    }

    // Ürün kontrolü
    if (tax._count.products > 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Ürünlerde kullanılan vergi oranı silinemez',
      })
    }

    // Vergi sil
    await prisma.tax.delete({
      where: { id },
    })

    return true
  }

  /**
   * Varsayılan vergiyi getirir
   * @param companyId - Şirket ID'si
   * @returns Varsayılan vergi bilgisi
   */
  static async getDefaultTax(companyId: string): Promise<TaxOutput | null> {
    const defaultTax = await prisma.tax.findFirst({
      where: { 
        companyId, 
        isDefault: true,
        active: true,
      },
    })

    if (!defaultTax) {
      return null
    }

    return {
      ...defaultTax,
      rate: defaultTax.rate.toString(),
    } as TaxOutput
  }

  /**
   * Aktif vergi listesini getirir
   * @param companyId - Şirket ID'si
   * @returns Aktif vergi listesi
   */
  static async getActiveTaxes(companyId: string): Promise<TaxOutput[]> {
    const taxes = await prisma.tax.findMany({
      where: { 
        companyId,
        active: true,
      },
      orderBy: [
        { isDefault: 'desc' },
        { name: 'asc' },
      ],
    })

    return taxes.map(tax => ({
      ...tax,
      rate: tax.rate.toString(),
    })) as TaxOutput[]
  }
}
