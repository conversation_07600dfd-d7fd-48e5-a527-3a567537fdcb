import React from 'react'
import { Loader2 } from 'lucide-react'

interface StartShiftButtonProps {
  onClick: () => void
  disabled?: boolean
  loading?: boolean
  className?: string
}

export function StartShiftButton({ 
  onClick, 
  disabled = false, 
  loading = false, 
  className = "" 
}: StartShiftButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        bg-[#025cca] h-[60px] w-full rounded-xl
        flex items-center justify-center
        hover:bg-[#0248a3] active:bg-[#023a8a]
        disabled:opacity-50 disabled:cursor-not-allowed
        transition-colors
        ${className}
      `}
      style={{ minHeight: '44px' }} // Touch-friendly minimum size
    >
      <div className="flex items-center gap-2">
        {loading && (
          <Loader2 className="w-4 h-4 text-white animate-spin" />
        )}
        <span className="font-inter font-semibold text-[16px] leading-[24px] text-white">
          {loading ? '<PERSON><PERSON><PERSON> ya<PERSON>lıyor...' : '<PERSON><PERSON><PERSON><PERSON>la'}
        </span>
      </div>
    </button>
  )
}
